<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Weather App</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-theme="light">
    <div class="container">
        <header class="header">
            <div class="header-top">
                <h1 class="title">
                    <i class="fas fa-cloud-sun weather-icon-animated"></i>
                    Enhanced Weather App
                </h1>
                <div class="header-controls">
                    <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="location-btn" id="locationBtn" title="Use current location">
                        <i class="fas fa-location-dot"></i>
                    </button>
                </div>
            </div>
            <p class="subtitle">Get real-time weather information and 5-day forecast</p>
        </header>

        <main class="main">
            <!-- Search Section -->
            <section class="search-section">
                <form class="search-form" id="searchForm">
                    <div class="search-input-container">
                        <input
                            type="text"
                            id="cityInput"
                            class="search-input"
                            placeholder="Enter city name..."
                            required
                            autocomplete="off"
                        >
                        <button type="submit" class="search-btn" id="searchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>

                <!-- Search History -->
                <div class="search-history" id="searchHistory">
                    <div class="history-header">
                        <span class="history-title">Recent Searches</span>
                        <button class="clear-history-btn" id="clearHistoryBtn" title="Clear history">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="history-items" id="historyItems"></div>
                </div>
            </section>

            <!-- Loading Indicator -->
            <div class="loading" id="loading">
                <div class="loading-content">
                    <div class="spinner-container">
                        <div class="spinner"></div>
                        <div class="shimmer-effect"></div>
                    </div>
                    <p class="loading-text">Fetching weather data...</p>
                </div>
            </div>

            <!-- Error Message -->
            <div class="error-message" id="errorMessage">
                <i class="fas fa-exclamation-triangle"></i>
                <p id="errorText"></p>
                <button class="retry-btn" id="retryBtn">
                    <i class="fas fa-redo"></i>
                    Try Again
                </button>
            </div>

            <!-- Weather Display -->
            <section class="weather-section" id="weatherSection">
                <div class="weather-card">
                    <!-- City and Date -->
                    <div class="weather-header">
                        <h2 class="city-name" id="cityName"></h2>
                        <p class="date" id="currentDate"></p>
                    </div>

                    <!-- Main Weather Info -->
                    <div class="weather-main">
                        <div class="temperature-section">
                            <div class="temperature-display">
                                <span class="temperature" id="temperature"></span>
                                <div class="unit-toggle">
                                    <button class="unit-btn active" id="celsiusBtn">°C</button>
                                    <button class="unit-btn" id="fahrenheitBtn">°F</button>
                                </div>
                            </div>
                            <div class="weather-icon-container">
                                <img class="weather-icon" id="weatherIcon" alt="Weather icon">
                            </div>
                        </div>
                        <div class="weather-description">
                            <p class="condition" id="weatherCondition"></p>
                            <p class="feels-like">Feels like <span id="feelsLike"></span></p>
                        </div>
                    </div>

                    <!-- Weather Details -->
                    <div class="weather-details">
                        <div class="detail-item">
                            <i class="fas fa-tint"></i>
                            <div class="detail-info">
                                <span class="detail-label">Humidity</span>
                                <span class="detail-value" id="humidity"></span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-wind"></i>
                            <div class="detail-info">
                                <span class="detail-label">Wind Speed</span>
                                <span class="detail-value" id="windSpeed"></span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-eye"></i>
                            <div class="detail-info">
                                <span class="detail-label">Visibility</span>
                                <span class="detail-value" id="visibility"></span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-thermometer-half"></i>
                            <div class="detail-info">
                                <span class="detail-label">Pressure</span>
                                <span class="detail-value" id="pressure"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 5-Day Forecast Section -->
            <section class="forecast-section" id="forecastSection">
                <div class="forecast-header">
                    <h3 class="forecast-title">
                        <i class="fas fa-calendar-days"></i>
                        5-Day Forecast
                    </h3>
                </div>
                <div class="forecast-container" id="forecastContainer">
                    <!-- Forecast cards will be dynamically generated -->
                </div>
            </section>
        </main>

        <footer class="footer">
            <p>&copy; 2024 Enhanced Weather App. Powered by OpenWeatherMap API</p>
        </footer>
    </div>

    <!-- Weather Icon Animations (SVG) -->
    <div class="weather-icons-svg" style="display: none;">
        <!-- Animated SVG icons will be loaded here -->
    </div>

    <script src="script.js"></script>
</body>
</html>

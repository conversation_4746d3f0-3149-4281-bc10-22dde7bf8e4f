<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather App</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">
                <i class="fas fa-cloud-sun"></i>
                Weather App
            </h1>
            <p class="subtitle">Get real-time weather information for any city</p>
        </header>

        <main class="main">
            <!-- Search Section -->
            <section class="search-section">
                <form class="search-form" id="searchForm">
                    <div class="search-input-container">
                        <input 
                            type="text" 
                            id="cityInput" 
                            class="search-input" 
                            placeholder="Enter city name..."
                            required
                            autocomplete="off"
                        >
                        <button type="submit" class="search-btn" id="searchBtn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </section>

            <!-- Loading Indicator -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Fetching weather data...</p>
            </div>

            <!-- Error Message -->
            <div class="error-message" id="errorMessage">
                <i class="fas fa-exclamation-triangle"></i>
                <p id="errorText"></p>
            </div>

            <!-- Weather Display -->
            <section class="weather-section" id="weatherSection">
                <div class="weather-card">
                    <!-- City and Date -->
                    <div class="weather-header">
                        <h2 class="city-name" id="cityName"></h2>
                        <p class="date" id="currentDate"></p>
                    </div>

                    <!-- Main Weather Info -->
                    <div class="weather-main">
                        <div class="temperature-section">
                            <div class="temperature-display">
                                <span class="temperature" id="temperature"></span>
                                <div class="unit-toggle">
                                    <button class="unit-btn active" id="celsiusBtn">°C</button>
                                    <button class="unit-btn" id="fahrenheitBtn">°F</button>
                                </div>
                            </div>
                            <div class="weather-icon-container">
                                <img class="weather-icon" id="weatherIcon" alt="Weather icon">
                            </div>
                        </div>
                        <div class="weather-description">
                            <p class="condition" id="weatherCondition"></p>
                            <p class="feels-like">Feels like <span id="feelsLike"></span></p>
                        </div>
                    </div>

                    <!-- Weather Details -->
                    <div class="weather-details">
                        <div class="detail-item">
                            <i class="fas fa-tint"></i>
                            <div class="detail-info">
                                <span class="detail-label">Humidity</span>
                                <span class="detail-value" id="humidity"></span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-wind"></i>
                            <div class="detail-info">
                                <span class="detail-label">Wind Speed</span>
                                <span class="detail-value" id="windSpeed"></span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-eye"></i>
                            <div class="detail-info">
                                <span class="detail-label">Visibility</span>
                                <span class="detail-value" id="visibility"></span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-thermometer-half"></i>
                            <div class="detail-info">
                                <span class="detail-label">Pressure</span>
                                <span class="detail-value" id="pressure"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <footer class="footer">
            <p>&copy; 2024 Weather App. Powered by OpenWeatherMap API</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>

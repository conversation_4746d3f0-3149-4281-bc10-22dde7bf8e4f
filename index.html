<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather Hub</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body data-theme="light">
    <!-- Floating Background Elements -->
    <div class="floating-orb"></div>
    <div class="floating-orb"></div>
    <div class="floating-orb"></div>

    <div class="container">
        <!-- Extended Header with Integrated Search -->
        <header class="header">
            <!-- Header Top Row -->
            <div class="header-top-row">
                <h1 class="title">
                    <i class="fas fa-cloud-sun weather-icon-animated"></i>
                    Weather Hub App
                </h1>
                <div class="header-controls">
                    <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="location-btn" id="locationBtn" title="Use current location">
                        <i class="fas fa-location-dot"></i>
                    </button>
                </div>
            </div>

            <!-- Header Subtitle -->
            <p class="subtitle">Get real-time weather information and 5-day forecast</p>

            <!-- Integrated Search Section -->
            <div class="header-search-integrated">
                <!-- Left Search History -->
                <div class="search-history-left" id="searchHistoryLeft">
                    <!-- Left side horizontal history rows will be populated by JavaScript -->
                </div>

                <!-- Central Search Area -->
                <div class="search-central">
                    <form class="search-form" id="searchForm">
                        <div class="search-input-container">
                            <input
                                type="text"
                                id="cityInput"
                                class="search-input"
                                placeholder="Enter city name..."
                                required
                                autocomplete="off"
                            >
                            <button type="submit" class="search-btn" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>

                    <!-- Clear History Button -->
                    <div class="clear-history-container">
                        <button class="clear-history-btn" id="clearHistoryBtn" title="Clear search history">
                            <i class="fas fa-trash"></i>
                            <span>Clear</span>
                        </button>
                    </div>
                </div>

                <!-- Right Search History -->
                <div class="search-history-right" id="searchHistoryRight">
                    <!-- Right side horizontal history rows will be populated by JavaScript -->
                </div>

                <!-- Hidden original search history for compatibility -->
                <div class="search-history" id="searchHistory" style="display: none;">
                    <div class="history-header">
                        <span class="history-title">Recent Searches</span>
                    </div>
                    <div class="history-items" id="historyItems"></div>
                </div>
            </div>
        </header>

        <main class="main">
            <!-- Welcome Section - Horizontal Layout -->
            <section class="welcome-section" id="welcomeSection">
                <div class="welcome-content">
                    <!-- Welcome Header -->
                    <div class="welcome-header">
                        <div class="welcome-message">
                            <h2 class="welcome-title">
                                <i class="fas fa-sun weather-pulse"></i>
                                Welcome to Your Weather Hub
                            </h2>
                            <p class="welcome-subtitle">Discover weather conditions and forecasts for any location worldwide</p>
                        </div>

                        <!-- Live Date and Time -->
                        <div class="live-datetime" id="liveDateTime">
                            <div class="current-time" id="currentTime"></div>
                            <div class="current-date" id="currentDate"></div>
                        </div>
                    </div>

                    <!-- Main Horizontal Content -->
                    <div class="welcome-horizontal-content">
                        <!-- Left Column -->
                        <div class="welcome-column welcome-left">
                            <!-- Weather Illustration -->
                            <div class="weather-illustration">
                                <div class="weather-scene">
                                    <div class="sun">
                                        <div class="sun-rays"></div>
                                    </div>
                                    <div class="clouds">
                                        <div class="cloud cloud-1"></div>
                                        <div class="cloud cloud-2"></div>
                                        <div class="cloud cloud-3"></div>
                                    </div>
                                    <div class="rain-drops" id="rainDrops"></div>
                                </div>
                            </div>

                            <!-- Quick Stats -->
                            <div class="quick-stats">
                                <div class="stat-item">
                                    <i class="fas fa-globe"></i>
                                    <span class="stat-number" id="citiesCount">0</span>
                                    <span class="stat-label">Cities Available</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-clock"></i>
                                    <span class="stat-number">24/7</span>
                                    <span class="stat-label">Live Updates</span>
                                </div>
                                <!--
                                <div class="stat-item">
                                    <i class="fas fa-calendar-week"></i>
                                    <span class="stat-number">5</span>
                                    <span class="stat-label">Day Forecast</span>
                                </div>-->
                            </div>
                        </div>

                        <!-- Center Column -->
                        <div class="welcome-column welcome-center">
                            <!-- Example Cities -->
                            <div class="example-cities">
                                <p class="example-title">Try searching for popular destinations:</p>
                                <div class="city-suggestions" id="citySuggestions">
                                    <button class="city-suggestion" data-city="Paris">
                                        <i class="fas fa-map-marker-alt"></i>
                                        Paris
                                    </button>
                                    <button class="city-suggestion" data-city="Tokyo">
                                        <i class="fas fa-map-marker-alt"></i>
                                        Tokyo
                                    </button>
                                    <button class="city-suggestion" data-city="New York">
                                        <i class="fas fa-map-marker-alt"></i>
                                        New York
                                    </button>
                                    <button class="city-suggestion" data-city="London">
                                        <i class="fas fa-map-marker-alt"></i>
                                        London
                                    </button>
                                    <button class="city-suggestion" data-city="Sydney">
                                        <i class="fas fa-map-marker-alt"></i>
                                        Sydney
                                    </button>
                                    <button class="city-suggestion" data-city="Dubai">
                                        <i class="fas fa-map-marker-alt"></i>
                                        Dubai
                                    </button>
                                </div>
                            </div>

                            <!-- Weather Fact -->
                            <div class="weather-fact" id="weatherFact">
                                <div class="fact-icon">
                                    <i class="fas fa-lightbulb"></i>
                                </div>
                                <div class="fact-content">
                                    <h4 class="fact-title">Weather Fact</h4>
                                    <p class="fact-text" id="factText"></p>
                                </div>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="welcome-column welcome-right">
                            <!-- Smart Features -->
                            <div class="smart-features">
                                <div class="feature-item">
                                    <div class="feature-icon">
                                        <i class="fas fa-brain"></i>
                                    </div>
                                    <div class="feature-content">
                                        <h4>Smart Suggestions</h4>
                                        <p>AI-powered location recommendations based on weather patterns</p>
                                    </div>
                                </div>
                                <div class="feature-item">
                                    <div class="feature-icon">
                                        <i class="fas fa-heart"></i>
                                    </div>
                                    <div class="feature-content">
                                        <h4>Weather Mood</h4>
                                        <p>Discover how weather affects your daily activities and mood</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Weather Insights -->
                            <div class="weather-insights">
                                <div class="insight-header">
                                    <i class="fas fa-chart-line"></i>
                                    <span>Today's Global Weather Insights</span>
                                </div>
                                <div class="insights-grid">
                                    <div class="insight-item">
                                        <span class="insight-value" id="globalTemp">--°</span>
                                        <span class="insight-label">Global Avg Temp</span>
                                    </div>
                                    <div class="insight-item">
                                        <span class="insight-value" id="activeStorms">--</span>
                                        <span class="insight-label">Active Storms</span>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Loading Indicator -->
            <div class="loading" id="loading">
                <div class="loading-content">
                    <div class="spinner-container">
                        <div class="spinner"></div>
                        <div class="shimmer-effect"></div>
                    </div>
                    <p class="loading-text">Fetching weather data...</p>
                </div>
            </div>

            <!-- Error Message -->
            <div class="error-message" id="errorMessage">
                <i class="fas fa-exclamation-triangle"></i>
                <p id="errorText"></p>
                <button class="retry-btn" id="retryBtn">
                    <i class="fas fa-redo"></i>
                    Try Again
                </button>
            </div>

            <!-- Unified Weather Display -->
            <section class="weather-section" id="weatherSection">
                <div class="unified-weather-card">
                    <!-- Back Button -->
                    <button class="back-btn" id="backBtn" title="Back to Home">
                        <i class="fas fa-arrow-left"></i>
                        <span>Back to Home</span>
                    </button>

                    <!-- City and Date Header -->
                    <div class="weather-header">
                        <h2 class="city-name" id="cityName"></h2>
                        <p class="date" id="currentDate"></p>
                    </div>

                    <!-- Current Weather Section -->
                    <div class="current-weather-section">
                        <!-- Main Weather Info -->
                        <div class="weather-main">
                            <div class="temperature-section">
                                <div class="temperature-display">
                                    <span class="temperature" id="temperature"></span>
                                    <div class="unit-toggle">
                                        <button class="unit-btn active" id="celsiusBtn">°C</button>
                                        <button class="unit-btn" id="fahrenheitBtn">°F</button>
                                    </div>
                                </div>
                                <div class="weather-icon-container">
                                    <img class="weather-icon" id="weatherIcon" alt="Weather icon">
                                </div>
                            </div>
                            <div class="weather-description">
                                <p class="condition" id="weatherCondition"></p>
                                <p class="feels-like">Feels like <span id="feelsLike"></span></p>
                            </div>
                        </div>

                        <!-- Weather Details Grid -->
                        <div class="weather-details">
                            <div class="detail-item">
                                <i class="fas fa-tint"></i>
                                <div class="detail-info">
                                    <span class="detail-label">Humidity</span>
                                    <span class="detail-value" id="humidity"></span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-wind"></i>
                                <div class="detail-info">
                                    <span class="detail-label">Wind Speed</span>
                                    <span class="detail-value" id="windSpeed"></span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-eye"></i>
                                <div class="detail-info">
                                    <span class="detail-label">Visibility</span>
                                    <span class="detail-value" id="visibility"></span>
                                </div>
                            </div>
                            <div class="detail-item">
                                <i class="fas fa-thermometer-half"></i>
                                <div class="detail-info">
                                    <span class="detail-label">Pressure</span>
                                    <span class="detail-value" id="pressure"></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Weather Mood -->
                    <div class="weather-mood" id="weatherMood">
                        <div class="mood-content">
                            <div class="mood-emoji" id="moodEmoji">😊</div>
                            <div class="mood-info">
                                <span class="mood-title">Weather Mood: <span id="moodName">Energetic</span></span>
                                <span class="mood-activity" id="moodActivity">Perfect for outdoor activities!</span>
                            </div>
                        </div>
                    </div>

                    <!-- 5-Day Forecast Section -->
                    <div class="forecast-section-integrated">
                        <div class="forecast-header">
                            <h3 class="forecast-title">
                                <i class="fas fa-calendar-days"></i>
                                5-Day Forecast
                            </h3>
                        </div>
                        <div class="forecast-container" id="forecastContainer">
                            <!-- Forecast cards will be dynamically generated -->
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <footer class="modern-footer">
            <!-- Animated Background -->
            <div class="footer-bg-animation">
                <div class="floating-particles"></div>
                <div class="gradient-waves"></div>
            </div>

            <!-- Footer Content -->
            <div class="footer-content">
                <!-- Weather Stats Section -->
                <div class="footer-section stats-section">
                    <h3 class="footer-title">
                        <i class="fas fa-chart-line"></i>
                        Live Weather Stats
                    </h3>
                    <div class="weather-stats">
                        <div class="stat-item">
                            <span class="stat-label">Cities Searched</span>
                            <span class="stat-value" id="citiesSearched">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">API Calls Today</span>
                            <span class="stat-value" id="apiCalls">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Active Users</span>
                            <span class="stat-value animated-counter" id="activeUsers">1,247</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Section -->
                <div class="footer-section actions-section">
                    <h3 class="footer-title">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </h3>
                    <div class="quick-actions">
                        <button class="action-btn" id="randomCityBtn">
                            <i class="fas fa-random"></i>
                            <span>Random City</span>
                        </button>
                        <button class="action-btn" id="weatherMapBtn">
                            <i class="fas fa-map"></i>
                            <span>Weather Map</span>
                        </button>
                        <button class="action-btn" id="shareWeatherBtn">
                            <i class="fas fa-share-alt"></i>
                            <span>Share Weather</span>
                        </button>
                        <button class="action-btn" id="exportDataBtn">
                            <i class="fas fa-download"></i>
                            <span>Export Data</span>
                        </button>
                    </div>
                </div>

                <!-- Weather Insights Section -->
                <div class="footer-section insights-section">
                    <h3 class="footer-title">
                        <i class="fas fa-lightbulb"></i>
                        Weather Insights
                    </h3>
                    <div class="weather-insights">
                        <div class="insight-item">
                            <i class="fas fa-thermometer-half"></i>
                            <div class="insight-content">
                                <span class="insight-label">Global Average</span>
                                <span class="insight-value">15.2°C</span>
                            </div>
                        </div>
                        <div class="insight-item">
                            <i class="fas fa-wind"></i>
                            <div class="insight-content">
                                <span class="insight-label">Wind Speed</span>
                                <span class="insight-value">12.5 km/h</span>
                            </div>
                        </div>
                        <div class="insight-item">
                            <i class="fas fa-eye"></i>
                            <div class="insight-content">
                                <span class="insight-label">Visibility</span>
                                <span class="insight-value">8.2 km</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social & Info Section -->
                <div class="footer-section social-section">
                    <h3 class="footer-title">
                        <i class="fas fa-heart"></i>
                        Connect & Share
                    </h3>
                    <div class="social-links">
                        <a href="#" class="social-link" title="Share on Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link" title="Share on Facebook">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" class="social-link" title="Share on LinkedIn">
                            <i class="fab fa-linkedin"></i>
                        </a>
                        <a href="#" class="social-link" title="GitHub Repository">
                            <i class="fab fa-github"></i>
                        </a>
                    </div>
                    <div class="app-info">
                        <p class="version">Version 2.0.1</p>
                        <p class="last-updated">Updated: <span id="lastUpdated">Just now</span></p>
                    </div>
                </div>
            </div>

            <!-- Footer Bottom -->
            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <div class="copyright">
                        <p>&copy; 2025 Weather Hub App. Crafted with <i class="fas fa-heart pulse-heart"></i> by Mohamed Amine Abid</p>
                        <p class="api-credit">Powered by <a href="https://openweathermap.org/" target="_blank" class="api-link">OpenWeatherMap API</a></p>
                    </div>
                    <div class="footer-controls">
                        <button class="footer-control-btn" id="footerThemeBtn" title="Toggle Footer Theme">
                            <i class="fas fa-palette"></i>
                        </button>
                        <button class="footer-control-btn" id="footerMinimizeBtn" title="Minimize Footer">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Magical Elements -->
            <div class="magical-elements">
                <div class="sparkles"></div>
                <div class="floating-icons">
                    <i class="fas fa-cloud weather-float"></i>
                    <i class="fas fa-sun weather-float"></i>
                    <i class="fas fa-snowflake weather-float"></i>
                    <i class="fas fa-bolt weather-float"></i>
                </div>
            </div>
        </footer>
    </div>

    <!-- Weather Icon Animations (SVG) -->
    <div class="weather-icons-svg" style="display: none;">
        <!-- Animated SVG icons will be loaded here -->
    </div>

    <script src="script.js"></script>
</body>
</html>

/**
 * Enhanced Weather App JavaScript
 * Features: 5-day forecast, search history, geolocation, dark mode, animated icons
 */

class EnhancedWeatherApp {
    constructor() {
        // API Configuration
        this.API_KEY = 'fbdb14521f927703e98682f84f59d6c4'; // Replace with your OpenWeatherMap API key
        this.BASE_URL = 'https://api.openweathermap.org/data/2.5/weather';
        this.FORECAST_URL = 'https://api.openweathermap.org/data/2.5/forecast';

        // App State
        this.currentUnit = this.loadPreference('temperatureUnit', 'celsius');
        this.currentTheme = this.loadPreference('theme', 'light');
        this.currentWeatherData = null;
        this.forecastData = null;
        this.searchHistoryData = this.loadSearchHistory();
        this.lastSearchedCity = null;

        // Welcome section state
        this.weatherFacts = this.getWeatherFacts();
        this.currentFactIndex = 0;
        this.liveTimeInterval = null;
        this.rainAnimationInterval = null;

        // Initialize app
        this.initializeElements();
        this.bindEvents();
        this.initializeTheme();
        this.initializeTemperatureUnit();
        this.initializeWelcomeSection();
        this.updateDateTime();
        this.displaySearchHistory();
    }

    /**
     * Initialize DOM elements
     */
    initializeElements() {
        // Form elements
        this.searchForm = document.getElementById('searchForm');
        this.cityInput = document.getElementById('cityInput');
        this.searchBtn = document.getElementById('searchBtn');

        // Control elements
        this.themeToggle = document.getElementById('themeToggle');
        this.locationBtn = document.getElementById('locationBtn');
        this.retryBtn = document.getElementById('retryBtn');

        // Display elements
        this.loading = document.getElementById('loading');
        this.errorMessage = document.getElementById('errorMessage');
        this.errorText = document.getElementById('errorText');
        this.weatherSection = document.getElementById('weatherSection');
        this.forecastContainer = document.getElementById('forecastContainer');
        this.backBtn = document.getElementById('backBtn');

        // Search history elements
        this.searchHistoryElement = document.getElementById('searchHistory');
        this.historyItems = document.getElementById('historyItems');
        this.clearHistoryBtn = document.getElementById('clearHistoryBtn');

        // Left and right search history elements
        this.searchHistoryLeft = document.getElementById('searchHistoryLeft');
        this.searchHistoryRight = document.getElementById('searchHistoryRight');

        // Welcome section elements
        this.welcomeSection = document.getElementById('welcomeSection');
        this.liveDateTime = document.getElementById('liveDateTime');
        this.currentTimeElement = document.getElementById('currentTime');
        this.currentDateElement = document.getElementById('currentDate');
        this.citySuggestions = document.getElementById('citySuggestions');
        this.factText = document.getElementById('factText');
        this.citiesCount = document.getElementById('citiesCount');
        this.rainDrops = document.getElementById('rainDrops');

        // Smart features elements
        this.globalTemp = document.getElementById('globalTemp');
        this.activeStorms = document.getElementById('activeStorms');

        // Weather mood elements
        this.weatherMood = document.getElementById('weatherMood');
        this.moodEmoji = document.getElementById('moodEmoji');
        this.moodName = document.getElementById('moodName');
        this.moodActivity = document.getElementById('moodActivity');

        // Weather data elements
        this.cityName = document.getElementById('cityName');
        this.currentDate = document.getElementById('currentDate');
        this.temperature = document.getElementById('temperature');
        this.weatherIcon = document.getElementById('weatherIcon');
        this.weatherCondition = document.getElementById('weatherCondition');
        this.feelsLike = document.getElementById('feelsLike');
        this.humidity = document.getElementById('humidity');
        this.windSpeed = document.getElementById('windSpeed');
        this.visibility = document.getElementById('visibility');
        this.pressure = document.getElementById('pressure');

        // Unit toggle buttons
        this.celsiusBtn = document.getElementById('celsiusBtn');
        this.fahrenheitBtn = document.getElementById('fahrenheitBtn');
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Search functionality
        this.searchForm.addEventListener('submit', (e) => this.handleSearch(e));
        this.cityInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleSearch(e);
            }
        });

        // Temperature unit toggle
        this.celsiusBtn.addEventListener('click', () => this.toggleUnit('celsius'));
        this.fahrenheitBtn.addEventListener('click', () => this.toggleUnit('fahrenheit'));

        // Theme and location controls
        this.themeToggle.addEventListener('click', () => this.toggleTheme());
        this.locationBtn.addEventListener('click', () => this.requestLocationWeather());
        this.retryBtn.addEventListener('click', () => this.retryLastSearch());

        // Back button
        this.backBtn.addEventListener('click', () => this.goBackToHome());

        // Search history
        this.clearHistoryBtn.addEventListener('click', () => this.clearSearchHistory());

        // City input suggestions (debounced)
        this.cityInput.addEventListener('input', this.debounce(() => {
            this.showSearchSuggestions();
        }, 300));

        // City suggestions
        this.citySuggestions.addEventListener('click', (e) => {
            if (e.target.classList.contains('city-suggestion') || e.target.closest('.city-suggestion')) {
                const button = e.target.closest('.city-suggestion') || e.target;
                const city = button.getAttribute('data-city');
                if (city) {
                    this.cityInput.value = city;
                    this.handleSearch(new Event('submit'));
                }
            }
        });
    }

    /**
     * Handle search form submission
     */
    async handleSearch(e) {
        e.preventDefault();

        const city = this.cityInput.value.trim();
        if (!city) {
            this.showError('Please enter a city name');
            return;
        }

        if (this.API_KEY === 'YOUR_API_KEY_HERE' || !this.API_KEY) {
            this.showError('Please add your OpenWeatherMap API key to the script.js file. Get your free API key at openweathermap.org/api');
            return;
        }

        this.lastSearchedCity = city;
        await this.fetchWeatherData(city);
    }

    /**
     * Retry the last search
     */
    async retryLastSearch() {
        if (this.lastSearchedCity) {
            await this.fetchWeatherData(this.lastSearchedCity);
        }
    }

    /**
     * Load user preferences from localStorage
     */
    loadPreference(key, defaultValue) {
        try {
            const value = localStorage.getItem(`weatherApp_${key}`);
            return value !== null ? value : defaultValue;
        } catch (error) {
            console.warn('Failed to load preference:', key, error);
            return defaultValue;
        }
    }

    /**
     * Save user preference to localStorage
     */
    savePreference(key, value) {
        try {
            localStorage.setItem(`weatherApp_${key}`, value);
        } catch (error) {
            console.warn('Failed to save preference:', key, error);
        }
    }

    /**
     * Load search history from localStorage
     */
    loadSearchHistory() {
        try {
            const history = localStorage.getItem('weatherApp_searchHistory');
            const loadedHistory = history ? JSON.parse(history) : [];

            // Add demo items if no history exists
            if (loadedHistory.length === 0) {
                return [
                    { searchTerm: 'New York', displayName: 'New York, US', timestamp: Date.now() - 1000 },
                    { searchTerm: 'London', displayName: 'London, GB', timestamp: Date.now() - 2000 },
                    { searchTerm: 'Tokyo', displayName: 'Tokyo, JP', timestamp: Date.now() - 3000 },
                    { searchTerm: 'Paris', displayName: 'Paris, FR', timestamp: Date.now() - 4000 },
                    { searchTerm: 'Sydney', displayName: 'Sydney, AU', timestamp: Date.now() - 5000 },
                    { searchTerm: 'Dubai', displayName: 'Dubai, AE', timestamp: Date.now() - 6000 }
                ];
            }

            return loadedHistory;
        } catch (error) {
            console.warn('Failed to load search history:', error);
            return [];
        }
    }

    /**
     * Save search history to localStorage
     */
    saveSearchHistory() {
        try {
            localStorage.setItem('weatherApp_searchHistory', JSON.stringify(this.searchHistoryData));
        } catch (error) {
            console.warn('Failed to save search history:', error);
        }
    }

    /**
     * Fetch current weather and forecast data
     */
    async fetchWeatherData(city) {
        this.showLoading();
        this.hideError();
        this.hideWeather();

        try {
            // Fetch both current weather and forecast data
            const [weatherResponse, forecastResponse] = await Promise.all([
                fetch(`${this.BASE_URL}?q=${encodeURIComponent(city)}&appid=${this.API_KEY}&units=metric`),
                fetch(`${this.FORECAST_URL}?q=${encodeURIComponent(city)}&appid=${this.API_KEY}&units=metric`)
            ]);

            if (!weatherResponse.ok) {
                if (weatherResponse.status === 404) {
                    throw new Error('City not found. Please check the spelling and try again.');
                } else if (weatherResponse.status === 401) {
                    throw new Error('Invalid API key. Please check your OpenWeatherMap API key.');
                } else {
                    throw new Error('Failed to fetch weather data. Please try again later.');
                }
            }

            const weatherData = await weatherResponse.json();
            const forecastData = await forecastResponse.json();

            this.currentWeatherData = weatherData;
            this.forecastData = forecastData;

            // Display the data
            this.displayWeatherData(weatherData);
            this.displayForecastData(forecastData);

            // Add to search history
            this.addToSearchHistory(city, weatherData.name, weatherData.sys.country);

            // Clear input after successful search
            this.cityInput.value = '';

        } catch (error) {
            console.error('Error fetching weather data:', error);
            this.showError(error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Fetch weather data by coordinates (for geolocation)
     */
    async fetchWeatherByCoords(lat, lon) {
        this.showLoading();
        this.hideError();
        this.hideWeather();

        try {
            const [weatherResponse, forecastResponse] = await Promise.all([
                fetch(`${this.BASE_URL}?lat=${lat}&lon=${lon}&appid=${this.API_KEY}&units=metric`),
                fetch(`${this.FORECAST_URL}?lat=${lat}&lon=${lon}&appid=${this.API_KEY}&units=metric`)
            ]);

            if (!weatherResponse.ok) {
                throw new Error('Failed to fetch weather data for your location.');
            }

            const weatherData = await weatherResponse.json();
            const forecastData = await forecastResponse.json();

            this.currentWeatherData = weatherData;
            this.forecastData = forecastData;

            this.displayWeatherData(weatherData);
            this.displayForecastData(forecastData);

            this.addToSearchHistory('Current Location', weatherData.name, weatherData.sys.country);

        } catch (error) {
            console.error('Error fetching location weather:', error);
            this.showError(error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Display current weather data
     */
    displayWeatherData(data) {
        // Update city name and date
        this.cityName.textContent = `${data.name}, ${data.sys.country}`;
        this.updateDateTime();

        // Update temperature
        this.updateTemperatureDisplay(data);

        // Update weather icon and condition with animation
        const iconCode = data.weather[0].icon;
        this.weatherIcon.src = `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
        this.weatherIcon.alt = data.weather[0].description;
        this.weatherIcon.classList.add('weather-icon-animated');
        this.weatherCondition.textContent = data.weather[0].description;

        // Update weather details
        this.humidity.textContent = `${data.main.humidity}%`;
        this.windSpeed.textContent = `${Math.round(data.wind.speed * 3.6)} km/h`;
        this.visibility.textContent = data.visibility ? `${(data.visibility / 1000).toFixed(1)} km` : 'N/A';
        this.pressure.textContent = `${data.main.pressure} hPa`;

        // Update weather mood
        this.displayWeatherMood(data);

        this.showWeather();
    }

    /**
     * Display 5-day forecast data
     */
    displayForecastData(data) {
        if (!data || !data.list) return;

        // Clear existing forecast
        this.forecastContainer.innerHTML = '';

        // Get daily forecasts (every 8th item = 24 hours apart)
        const dailyForecasts = [];
        for (let i = 0; i < data.list.length; i += 8) {
            if (dailyForecasts.length >= 5) break;
            dailyForecasts.push(data.list[i]);
        }

        // Create forecast cards
        dailyForecasts.forEach((forecast, index) => {
            const forecastCard = this.createForecastCard(forecast, index === 0);
            this.forecastContainer.appendChild(forecastCard);
        });

        this.showForecast();
    }

    /**
     * Create a forecast card element
     */
    createForecastCard(forecast, isToday = false) {
        const card = document.createElement('div');
        card.className = 'forecast-card';

        const date = new Date(forecast.dt * 1000);
        const dayName = isToday ? 'Today' : date.toLocaleDateString('en-US', { weekday: 'short' });

        const tempMin = Math.round(forecast.main.temp_min);
        const tempMax = Math.round(forecast.main.temp_max);

        const iconCode = forecast.weather[0].icon;
        const description = forecast.weather[0].description;

        card.innerHTML = `
            <div class="forecast-day">${dayName}</div>
            <img class="forecast-icon" src="https://openweathermap.org/img/wn/${iconCode}@2x.png" alt="${description}">
            <div class="forecast-temps">
                <span class="forecast-high">${this.formatTemperature(tempMax)}°</span>
                <span class="forecast-low">${this.formatTemperature(tempMin)}°</span>
            </div>
            <div class="forecast-desc">${description}</div>
        `;

        return card;
    }

    /**
     * Format temperature based on current unit
     */
    formatTemperature(tempCelsius) {
        if (this.currentUnit === 'fahrenheit') {
            return Math.round((tempCelsius * 9/5) + 32);
        }
        return Math.round(tempCelsius);
    }

    /**
     * Update temperature display based on current unit
     */
    updateTemperatureDisplay(data) {
        const temp = Math.round(data.main.temp);
        const feelsLikeTemp = Math.round(data.main.feels_like);

        if (this.currentUnit === 'celsius') {
            this.temperature.textContent = `${temp}°`;
            this.feelsLike.textContent = `${feelsLikeTemp}°C`;
        } else {
            const tempF = Math.round((temp * 9/5) + 32);
            const feelsLikeTempF = Math.round((feelsLikeTemp * 9/5) + 32);
            this.temperature.textContent = `${tempF}°`;
            this.feelsLike.textContent = `${feelsLikeTempF}°F`;
        }
    }

    /**
     * Toggle temperature unit and update display
     */
    toggleUnit(unit) {
        if (unit === this.currentUnit) return;

        this.currentUnit = unit;
        this.savePreference('temperatureUnit', unit);

        // Update button states
        this.celsiusBtn.classList.toggle('active', unit === 'celsius');
        this.fahrenheitBtn.classList.toggle('active', unit === 'fahrenheit');

        // Update temperature display if weather data exists
        if (this.currentWeatherData) {
            this.updateTemperatureDisplay(this.currentWeatherData);
        }

        // Update forecast display
        if (this.forecastData) {
            this.displayForecastData(this.forecastData);
        }
    }

    /**
     * Initialize temperature unit from saved preference
     */
    initializeTemperatureUnit() {
        this.celsiusBtn.classList.toggle('active', this.currentUnit === 'celsius');
        this.fahrenheitBtn.classList.toggle('active', this.currentUnit === 'fahrenheit');
    }

    /**
     * Toggle between light and dark themes
     */
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.savePreference('theme', this.currentTheme);
        this.applyTheme();
    }

    /**
     * Initialize theme from saved preference
     */
    initializeTheme() {
        this.applyTheme();
    }

    /**
     * Apply the current theme
     */
    applyTheme() {
        document.body.setAttribute('data-theme', this.currentTheme);
        const icon = this.themeToggle.querySelector('i');

        if (this.currentTheme === 'dark') {
            icon.className = 'fas fa-sun';
            this.themeToggle.title = 'Switch to light mode';
        } else {
            icon.className = 'fas fa-moon';
            this.themeToggle.title = 'Switch to dark mode';
        }
    }

    /**
     * Request user's current location weather
     */
    requestLocationWeather() {
        if (!navigator.geolocation) {
            this.showError('Geolocation is not supported by this browser.');
            return;
        }

        this.locationBtn.disabled = true;
        this.locationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        navigator.geolocation.getCurrentPosition(
            (position) => {
                const { latitude, longitude } = position.coords;
                this.fetchWeatherByCoords(latitude, longitude);
                this.locationBtn.disabled = false;
                this.locationBtn.innerHTML = '<i class="fas fa-location-dot"></i>';
            },
            (error) => {
                console.error('Geolocation error:', error);
                let errorMessage = 'Unable to get your location. ';

                switch (error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage += 'Please allow location access.';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage += 'Location information unavailable.';
                        break;
                    case error.TIMEOUT:
                        errorMessage += 'Location request timed out.';
                        break;
                    default:
                        errorMessage += 'An unknown error occurred.';
                        break;
                }

                this.showError(errorMessage);
                this.locationBtn.disabled = false;
                this.locationBtn.innerHTML = '<i class="fas fa-location-dot"></i>';
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 300000 // 5 minutes
            }
        );
    }

    /**
     * Add city to search history
     */
    addToSearchHistory(searchTerm, cityName, country) {
        const historyItem = {
            searchTerm,
            displayName: `${cityName}, ${country}`,
            timestamp: Date.now()
        };

        // Remove existing entry if it exists
        this.searchHistoryData = this.searchHistoryData.filter(item =>
            item.displayName !== historyItem.displayName
        );

        // Add to beginning of array
        this.searchHistoryData.unshift(historyItem);

        // Keep only last 10 searches
        this.searchHistoryData = this.searchHistoryData.slice(0, 10);

        this.saveSearchHistory();
        this.displaySearchHistory();
    }

    /**
     * Display search history in left and right horizontal rows
     */
    displaySearchHistory() {
        // Clear existing items
        this.searchHistoryLeft.innerHTML = '';
        this.searchHistoryRight.innerHTML = '';

        if (this.searchHistoryData.length === 0) {
            return;
        }

        // Split items between left and right sides
        const maxItemsPerSide = 3;
        const leftItems = this.searchHistoryData.slice(0, maxItemsPerSide);
        const rightItems = this.searchHistoryData.slice(maxItemsPerSide, maxItemsPerSide * 2);

        // Create horizontal rows for left side
        this.createHorizontalRows(this.searchHistoryLeft, leftItems, 'left');

        // Create horizontal rows for right side
        this.createHorizontalRows(this.searchHistoryRight, rightItems, 'right');

        // Also populate the hidden original for compatibility
        this.historyItems.innerHTML = '';
        this.searchHistoryData.forEach(item => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            historyItem.textContent = item.displayName;
            historyItem.addEventListener('click', () => {
                this.cityInput.value = item.searchTerm;
                this.handleSearch(new Event('submit'));
            });
            this.historyItems.appendChild(historyItem);
        });
    }

    /**
     * Create horizontal rows of history items
     */
    createHorizontalRows(container, items, side) {
        if (items.length === 0) return;

        // Create rows with 2 items each
        const itemsPerRow = 2;
        for (let i = 0; i < items.length; i += itemsPerRow) {
            const row = document.createElement('div');
            row.className = 'history-row';

            const rowItems = items.slice(i, i + itemsPerRow);
            rowItems.forEach((item, index) => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-side-item';
                historyItem.textContent = item.displayName;
                historyItem.title = `Search for ${item.displayName}`;
                historyItem.style.animationDelay = `${(i + index) * 0.1}s`;
                historyItem.addEventListener('click', () => {
                    this.cityInput.value = item.searchTerm;
                    this.handleSearch(new Event('submit'));
                });
                row.appendChild(historyItem);
            });

            container.appendChild(row);
        }
    }

    /**
     * Clear search history
     */
    clearSearchHistory() {
        this.searchHistoryData = [];
        this.saveSearchHistory();
        this.displaySearchHistory();
    }

    /**
     * Show search suggestions (placeholder for future enhancement)
     */
    showSearchSuggestions() {
        // This could be enhanced with a city suggestions API
        // For now, just show search history when user starts typing
        if (this.cityInput.value.length > 0 && this.searchHistoryData.length > 0) {
            this.displaySearchHistory();
        }
    }

    /**
     * Update current date and time display
     */
    updateDateTime() {
        const now = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        this.currentDate.textContent = now.toLocaleDateString('en-US', options);
    }

    /**
     * Debounce function to limit API calls
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // UI State Management Methods

    /**
     * Show loading indicator
     */
    showLoading() {
        this.loading.classList.add('show');
        this.searchBtn.disabled = true;
    }

    /**
     * Hide loading indicator
     */
    hideLoading() {
        this.loading.classList.remove('show');
        this.searchBtn.disabled = false;
    }

    /**
     * Show error message
     */
    showError(message) {
        this.errorText.textContent = message;
        this.errorMessage.classList.add('show');
    }

    /**
     * Hide error message
     */
    hideError() {
        this.errorMessage.classList.remove('show');
    }

    /**
     * Show weather section (unified card)
     */
    showWeather() {
        this.weatherSection.classList.add('show');
        this.hideWelcomeSection();
    }

    /**
     * Hide weather section (unified card)
     */
    hideWeather() {
        this.weatherSection.classList.remove('show');
    }

    /**
     * Go back to home page (welcome section)
     */
    goBackToHome() {
        this.hideWeather();
        this.showWelcomeSection();
        this.cityInput.value = '';
        this.hideError();
    }

    /**
     * Show forecast section (now part of unified card)
     */
    showForecast() {
        // Forecast is now integrated into the unified card
        // No separate action needed
    }

    /**
     * Hide forecast section (now part of unified card)
     */
    hideForecast() {
        // Forecast is now integrated into the unified card
        // No separate action needed
    }

    // Welcome Section Methods

    /**
     * Initialize welcome section with all features
     */
    initializeWelcomeSection() {
        this.startLiveTime();
        this.displayRandomWeatherFact();
        this.animateCitiesCounter();
        this.startWeatherAnimation();
        this.initializeGlobalInsights();
        this.updateSmartSuggestions();
    }

    /**
     * Get array of weather facts
     */
    getWeatherFacts() {
        return [
            "Lightning strikes the Earth about 100 times per second!",
            "A single cloud can weigh more than a million pounds.",
            "The fastest recorded wind speed was 253 mph during Tropical Cyclone Olivia.",
            "Raindrops are not tear-shaped - they're actually round!",
            "Antarctica is technically a desert because it receives so little precipitation.",
            "The highest temperature ever recorded was 134°F (56.7°C) in Death Valley.",
            "Snowflakes can take up to an hour to reach the ground.",
            "A bolt of lightning is 5 times hotter than the surface of the sun.",
            "The largest hailstone ever recorded was 8 inches in diameter.",
            "Tornadoes can spin at speeds of up to 300 mph.",
            "The wettest place on Earth receives over 460 inches of rain per year.",
            "Weather satellites orbit Earth at about 22,300 miles above the equator.",
            "A hurricane releases the energy equivalent of 10 atomic bombs per second.",
            "The coldest temperature ever recorded was -128.6°F (-89.2°C) in Antarctica.",
            "Fog is essentially a cloud that touches the ground.",
            "The eye of a hurricane can be 20-40 miles wide and completely calm.",
            "Weather balloons can reach heights of over 100,000 feet.",
            "A single thunderstorm can contain enough energy to power a city for weeks.",
            "The term 'precipitation' includes rain, snow, sleet, and hail.",
            "Meteorologists use Doppler radar to track precipitation and wind patterns."
        ];
    }

    /**
     * Display a random weather fact
     */
    displayRandomWeatherFact() {
        const facts = this.getWeatherFacts();
        const randomIndex = Math.floor(Math.random() * facts.length);
        this.factText.textContent = facts[randomIndex];

        // Add typing animation
        this.factText.style.opacity = '0';
        setTimeout(() => {
            this.factText.style.opacity = '1';
            this.factText.style.animation = 'fadeIn 0.5s ease';
        }, 100);
    }

    /**
     * Start live time display
     */
    startLiveTime() {
        this.updateLiveTime();
        this.liveTimeInterval = setInterval(() => {
            this.updateLiveTime();
        }, 1000);
    }

    /**
     * Update live time display
     */
    updateLiveTime() {
        const now = new Date();

        // Format time
        const timeOptions = {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: true
        };
        this.currentTimeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);

        // Format date
        const dateOptions = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        this.currentDateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
    }

    /**
     * Animate cities counter
     */
    animateCitiesCounter() {
        const targetCount = 200000; // Approximate number of cities in OpenWeatherMap
        const duration = 2000; // 2 seconds
        const steps = 60;
        const increment = targetCount / steps;
        let currentCount = 0;

        const counter = setInterval(() => {
            currentCount += increment;
            if (currentCount >= targetCount) {
                currentCount = targetCount;
                clearInterval(counter);
            }
            this.citiesCount.textContent = Math.floor(currentCount).toLocaleString();
        }, duration / steps);
    }

    /**
     * Start weather animation effects
     */
    startWeatherAnimation() {
        // Random weather effects
        setTimeout(() => {
            this.triggerRandomWeatherEffect();
        }, 3000);

        // Repeat every 10 seconds
        setInterval(() => {
            this.triggerRandomWeatherEffect();
        }, 10000);
    }

    /**
     * Trigger random weather animation effect
     */
    triggerRandomWeatherEffect() {
        const effects = ['rain', 'clear', 'cloudy'];
        const randomEffect = effects[Math.floor(Math.random() * effects.length)];

        switch (randomEffect) {
            case 'rain':
                this.createRainEffect();
                break;
            case 'clear':
                this.clearWeatherEffects();
                break;
            case 'cloudy':
                this.enhanceCloudAnimation();
                break;
        }
    }

    /**
     * Create rain animation effect
     */
    createRainEffect() {
        this.rainDrops.classList.add('active');
        this.rainDrops.innerHTML = '';

        // Create multiple rain drops
        for (let i = 0; i < 20; i++) {
            const drop = document.createElement('div');
            drop.className = 'rain-drop';
            drop.style.left = Math.random() * 100 + '%';
            drop.style.animationDelay = Math.random() * 1 + 's';
            drop.style.animationDuration = (Math.random() * 0.5 + 0.5) + 's';
            this.rainDrops.appendChild(drop);
        }

        // Stop rain after 3 seconds
        setTimeout(() => {
            this.rainDrops.classList.remove('active');
        }, 3000);
    }

    /**
     * Clear weather effects
     */
    clearWeatherEffects() {
        this.rainDrops.classList.remove('active');
    }

    /**
     * Enhance cloud animation
     */
    enhanceCloudAnimation() {
        const clouds = document.querySelectorAll('.cloud');
        clouds.forEach((cloud, index) => {
            cloud.style.animation = `float ${6 + index}s ease-in-out infinite`;
        });
    }

    /**
     * Hide welcome section when weather is displayed
     */
    hideWelcomeSection() {
        this.welcomeSection.classList.add('hidden');
        if (this.liveTimeInterval) {
            clearInterval(this.liveTimeInterval);
        }
    }

    /**
     * Show welcome section
     */
    showWelcomeSection() {
        this.welcomeSection.classList.remove('hidden');
        this.startLiveTime();
    }

    /**
     * Initialize global weather insights with simulated data
     */
    initializeGlobalInsights() {
        // Simulate global temperature (realistic average)
        const globalTemp = (Math.random() * 10 + 10).toFixed(1); // 10-20°C
        this.globalTemp.textContent = `${globalTemp}°C`;

        // Simulate active storms count
        const storms = Math.floor(Math.random() * 15 + 5); // 5-20 storms
        this.activeStorms.textContent = storms.toString();



        // Animate the values
        this.animateInsightValues();
    }

    /**
     * Animate insight values with counting effect
     */
    animateInsightValues() {
        // Animate global temperature
        this.animateValue(this.globalTemp, 0, parseFloat(this.globalTemp.textContent), '°C');

        // Animate storms count
        this.animateValue(this.activeStorms, 0, parseInt(this.activeStorms.textContent), '');
    }

    /**
     * Animate a numeric value with counting effect
     */
    animateValue(element, start, end, suffix) {
        const duration = 1500;
        const steps = 30;
        const increment = (end - start) / steps;
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= end) {
                current = end;
                clearInterval(timer);
            }

            if (suffix === '°C') {
                element.textContent = `${current.toFixed(1)}${suffix}`;
            } else {
                element.textContent = `${Math.floor(current)}${suffix}`;
            }
        }, duration / steps);
    }

    /**
     * Get weather mood based on conditions
     */
    getWeatherMood(weatherData) {
        const condition = weatherData.weather[0].main.toLowerCase();
        const temp = weatherData.main.temp;

        const moods = {
            clear: {
                emoji: '😊',
                mood: 'Energetic',
                activity: 'Perfect for outdoor activities!',
                color: '#FFD700'
            },
            clouds: {
                emoji: '😌',
                mood: 'Calm',
                activity: 'Great for reading or indoor hobbies.',
                color: '#87CEEB'
            },
            rain: {
                emoji: '🌧️',
                mood: 'Cozy',
                activity: 'Perfect for staying in with hot cocoa.',
                color: '#4682B4'
            },
            snow: {
                emoji: '❄️',
                mood: 'Peaceful',
                activity: 'Ideal for winter sports or warm drinks.',
                color: '#B0E0E6'
            },
            thunderstorm: {
                emoji: '⚡',
                mood: 'Dramatic',
                activity: 'Stay safe indoors and enjoy the show!',
                color: '#483D8B'
            }
        };

        let selectedMood = moods.clear; // default

        if (condition.includes('rain')) selectedMood = moods.rain;
        else if (condition.includes('snow')) selectedMood = moods.snow;
        else if (condition.includes('thunder')) selectedMood = moods.thunderstorm;
        else if (condition.includes('cloud')) selectedMood = moods.clouds;
        else if (temp > 25) selectedMood = moods.clear;

        return selectedMood;
    }

    /**
     * Generate smart city suggestions based on season and preferences
     */
    generateSmartSuggestions() {
        const month = new Date().getMonth();
        const season = this.getCurrentSeason(month);

        const seasonalSuggestions = {
            spring: ['Amsterdam', 'Kyoto', 'Istanbul', 'Barcelona', 'Prague'],
            summer: ['Santorini', 'Bali', 'Nice', 'Dubrovnik', 'Reykjavik'],
            autumn: ['New York', 'Munich', 'Seoul', 'Montreal', 'Edinburgh'],
            winter: ['Vienna', 'Salzburg', 'Quebec City', 'Lapland', 'Zurich']
        };

        return seasonalSuggestions[season] || seasonalSuggestions.spring;
    }

    /**
     * Get current season based on month
     */
    getCurrentSeason(month) {
        if (month >= 2 && month <= 4) return 'spring';
        if (month >= 5 && month <= 7) return 'summer';
        if (month >= 8 && month <= 10) return 'autumn';
        return 'winter';
    }

    /**
     * Update city suggestions with smart recommendations
     */
    updateSmartSuggestions() {
        const suggestions = this.generateSmartSuggestions();
        const buttons = this.citySuggestions.querySelectorAll('.city-suggestion');

        suggestions.slice(0, buttons.length).forEach((city, index) => {
            if (buttons[index]) {
                buttons[index].setAttribute('data-city', city);
                buttons[index].innerHTML = `<i class="fas fa-map-marker-alt"></i>${city}`;
            }
        });
    }

    /**
     * Display weather mood based on current conditions
     */
    displayWeatherMood(weatherData) {
        const mood = this.getWeatherMood(weatherData);

        this.moodEmoji.textContent = mood.emoji;
        this.moodName.textContent = mood.mood;
        this.moodActivity.textContent = mood.activity;

        // Add color theme to mood container
        this.weatherMood.style.borderColor = mood.color + '40'; // 40 for transparency
        this.weatherMood.style.background = `linear-gradient(135deg, ${mood.color}20, ${mood.color}10)`;

        // Animate mood display
        this.weatherMood.style.animation = 'none';
        setTimeout(() => {
            this.weatherMood.style.animation = 'moodPulse 3s ease-in-out infinite';
        }, 100);
    }
}

/**
 * Initialize the Enhanced Weather App when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
    new EnhancedWeatherApp();
});

/**
 * Additional utility functions and enhancements
 */

// Add smooth scroll animations when elements come into view
function addScrollAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    // Observe weather cards and sections
    document.querySelectorAll('.weather-card, .search-section, .forecast-section').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + K to focus search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        document.getElementById('cityInput').focus();
    }

    // Escape to clear search and hide history
    if (e.key === 'Escape') {
        const cityInput = document.getElementById('cityInput');
        const searchHistoryElement = document.getElementById('searchHistory');
        cityInput.blur();
        cityInput.value = '';
        searchHistoryElement.classList.remove('show');
    }
});

// Add service worker for offline functionality (optional enhancement)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        // Service worker registration would go here
        // This is a placeholder for future offline functionality
    });
}

// Initialize scroll animations after DOM load
document.addEventListener('DOMContentLoaded', addScrollAnimations);

// Add error handling for uncaught errors
window.addEventListener('error', (e) => {
    console.error('Uncaught error:', e.error);
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled promise rejection:', e.reason);
});

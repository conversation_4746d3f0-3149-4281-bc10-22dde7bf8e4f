// Weather App JavaScript

class WeatherApp {
    constructor() {
        // You need to get your API key from https://openweathermap.org/api
        this.API_KEY = '********************************'; // Replace with your actual API key
        this.BASE_URL = 'https://api.openweathermap.org/data/2.5/weather';
        
        this.currentUnit = 'celsius';
        this.currentWeatherData = null;
        
        this.initializeElements();
        this.bindEvents();
        this.updateDateTime();
    }

    initializeElements() {
        // Form elements
        this.searchForm = document.getElementById('searchForm');
        this.cityInput = document.getElementById('cityInput');
        this.searchBtn = document.getElementById('searchBtn');
        
        // Display elements
        this.loading = document.getElementById('loading');
        this.errorMessage = document.getElementById('errorMessage');
        this.errorText = document.getElementById('errorText');
        this.weatherSection = document.getElementById('weatherSection');
        
        // Weather data elements
        this.cityName = document.getElementById('cityName');
        this.currentDate = document.getElementById('currentDate');
        this.temperature = document.getElementById('temperature');
        this.weatherIcon = document.getElementById('weatherIcon');
        this.weatherCondition = document.getElementById('weatherCondition');
        this.feelsLike = document.getElementById('feelsLike');
        this.humidity = document.getElementById('humidity');
        this.windSpeed = document.getElementById('windSpeed');
        this.visibility = document.getElementById('visibility');
        this.pressure = document.getElementById('pressure');
        
        // Unit toggle buttons
        this.celsiusBtn = document.getElementById('celsiusBtn');
        this.fahrenheitBtn = document.getElementById('fahrenheitBtn');
    }

    bindEvents() {
        this.searchForm.addEventListener('submit', (e) => this.handleSearch(e));
        this.celsiusBtn.addEventListener('click', () => this.toggleUnit('celsius'));
        this.fahrenheitBtn.addEventListener('click', () => this.toggleUnit('fahrenheit'));
        
        // Allow Enter key to trigger search
        this.cityInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleSearch(e);
            }
        });
    }

    async handleSearch(e) {
        e.preventDefault();
        
        const city = this.cityInput.value.trim();
        if (!city) {
            this.showError('Please enter a city name');
            return;
        }

        if (this.API_KEY === 'YOUR_API_KEY_HERE') {
            this.showError('Please add your OpenWeatherMap API key to the script.js file');
            return;
        }

        await this.fetchWeatherData(city);
    }

    async fetchWeatherData(city) {
        this.showLoading();
        this.hideError();
        this.hideWeather();

        try {
            const url = `${this.BASE_URL}?q=${encodeURIComponent(city)}&appid=${this.API_KEY}&units=metric`;
            const response = await fetch(url);
            
            if (!response.ok) {
                if (response.status === 404) {
                    throw new Error('City not found. Please check the spelling and try again.');
                } else if (response.status === 401) {
                    throw new Error('Invalid API key. Please check your OpenWeatherMap API key.');
                } else {
                    throw new Error('Failed to fetch weather data. Please try again later.');
                }
            }

            const data = await response.json();
            this.currentWeatherData = data;
            this.displayWeatherData(data);
            this.cityInput.value = ''; // Clear input after successful search
            
        } catch (error) {
            console.error('Error fetching weather data:', error);
            this.showError(error.message);
        } finally {
            this.hideLoading();
        }
    }

    displayWeatherData(data) {
        // Update city name and date
        this.cityName.textContent = `${data.name}, ${data.sys.country}`;
        this.updateDateTime();

        // Update temperature
        this.updateTemperatureDisplay(data);

        // Update weather icon and condition
        const iconCode = data.weather[0].icon;
        this.weatherIcon.src = `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
        this.weatherIcon.alt = data.weather[0].description;
        this.weatherCondition.textContent = data.weather[0].description;

        // Update weather details
        this.humidity.textContent = `${data.main.humidity}%`;
        this.windSpeed.textContent = `${Math.round(data.wind.speed * 3.6)} km/h`; // Convert m/s to km/h
        this.visibility.textContent = data.visibility ? `${(data.visibility / 1000).toFixed(1)} km` : 'N/A';
        this.pressure.textContent = `${data.main.pressure} hPa`;

        this.showWeather();
    }

    updateTemperatureDisplay(data) {
        const temp = Math.round(data.main.temp);
        const feelsLikeTemp = Math.round(data.main.feels_like);

        if (this.currentUnit === 'celsius') {
            this.temperature.textContent = `${temp}°`;
            this.feelsLike.textContent = `${feelsLikeTemp}°C`;
        } else {
            const tempF = Math.round((temp * 9/5) + 32);
            const feelsLikeTempF = Math.round((feelsLikeTemp * 9/5) + 32);
            this.temperature.textContent = `${tempF}°`;
            this.feelsLike.textContent = `${feelsLikeTempF}°F`;
        }
    }

    toggleUnit(unit) {
        if (unit === this.currentUnit) return;

        this.currentUnit = unit;
        
        // Update button states
        this.celsiusBtn.classList.toggle('active', unit === 'celsius');
        this.fahrenheitBtn.classList.toggle('active', unit === 'fahrenheit');

        // Update temperature display if weather data exists
        if (this.currentWeatherData) {
            this.updateTemperatureDisplay(this.currentWeatherData);
        }
    }

    updateDateTime() {
        const now = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        this.currentDate.textContent = now.toLocaleDateString('en-US', options);
    }

    showLoading() {
        this.loading.classList.add('show');
        this.searchBtn.disabled = true;
    }

    hideLoading() {
        this.loading.classList.remove('show');
        this.searchBtn.disabled = false;
    }

    showError(message) {
        this.errorText.textContent = message;
        this.errorMessage.classList.add('show');
    }

    hideError() {
        this.errorMessage.classList.remove('show');
    }

    showWeather() {
        this.weatherSection.classList.add('show');
    }

    hideWeather() {
        this.weatherSection.classList.remove('show');
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new WeatherApp();
});

// Additional utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Add some nice animations when elements come into view
function addScrollAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });

    document.querySelectorAll('.weather-card, .search-section').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Initialize scroll animations after DOM load
document.addEventListener('DOMContentLoaded', addScrollAnimations);

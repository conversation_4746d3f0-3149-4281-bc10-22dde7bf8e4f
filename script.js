/**
 * Enhanced Weather App JavaScript
 * Features: 5-day forecast, search history, geolocation, dark mode, animated icons
 */

class EnhancedWeatherApp {
    constructor() {
        // API Configuration
        this.API_KEY = 'YOUR_API_KEY_HERE'; // Replace with your OpenWeatherMap API key
        this.BASE_URL = 'https://api.openweathermap.org/data/2.5/weather';
        this.FORECAST_URL = 'https://api.openweathermap.org/data/2.5/forecast';

        // App State
        this.currentUnit = this.loadPreference('temperatureUnit', 'celsius');
        this.currentTheme = this.loadPreference('theme', 'light');
        this.currentWeatherData = null;
        this.forecastData = null;
        this.searchHistory = this.loadSearchHistory();
        this.lastSearchedCity = null;

        // Initialize app
        this.initializeElements();
        this.bindEvents();
        this.initializeTheme();
        this.initializeTemperatureUnit();
        this.updateDateTime();
        this.displaySearchHistory();
        this.requestLocationWeather();
    }

    /**
     * Initialize DOM elements
     */
    initializeElements() {
        // Form elements
        this.searchForm = document.getElementById('searchForm');
        this.cityInput = document.getElementById('cityInput');
        this.searchBtn = document.getElementById('searchBtn');

        // Control elements
        this.themeToggle = document.getElementById('themeToggle');
        this.locationBtn = document.getElementById('locationBtn');
        this.retryBtn = document.getElementById('retryBtn');

        // Display elements
        this.loading = document.getElementById('loading');
        this.errorMessage = document.getElementById('errorMessage');
        this.errorText = document.getElementById('errorText');
        this.weatherSection = document.getElementById('weatherSection');
        this.forecastSection = document.getElementById('forecastSection');
        this.forecastContainer = document.getElementById('forecastContainer');

        // Search history elements
        this.searchHistory = document.getElementById('searchHistory');
        this.historyItems = document.getElementById('historyItems');
        this.clearHistoryBtn = document.getElementById('clearHistoryBtn');

        // Weather data elements
        this.cityName = document.getElementById('cityName');
        this.currentDate = document.getElementById('currentDate');
        this.temperature = document.getElementById('temperature');
        this.weatherIcon = document.getElementById('weatherIcon');
        this.weatherCondition = document.getElementById('weatherCondition');
        this.feelsLike = document.getElementById('feelsLike');
        this.humidity = document.getElementById('humidity');
        this.windSpeed = document.getElementById('windSpeed');
        this.visibility = document.getElementById('visibility');
        this.pressure = document.getElementById('pressure');

        // Unit toggle buttons
        this.celsiusBtn = document.getElementById('celsiusBtn');
        this.fahrenheitBtn = document.getElementById('fahrenheitBtn');
    }

    /**
     * Bind event listeners
     */
    bindEvents() {
        // Search functionality
        this.searchForm.addEventListener('submit', (e) => this.handleSearch(e));
        this.cityInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleSearch(e);
            }
        });

        // Temperature unit toggle
        this.celsiusBtn.addEventListener('click', () => this.toggleUnit('celsius'));
        this.fahrenheitBtn.addEventListener('click', () => this.toggleUnit('fahrenheit'));

        // Theme and location controls
        this.themeToggle.addEventListener('click', () => this.toggleTheme());
        this.locationBtn.addEventListener('click', () => this.requestLocationWeather());
        this.retryBtn.addEventListener('click', () => this.retryLastSearch());

        // Search history
        this.clearHistoryBtn.addEventListener('click', () => this.clearSearchHistory());

        // City input suggestions (debounced)
        this.cityInput.addEventListener('input', this.debounce(() => {
            this.showSearchSuggestions();
        }, 300));
    }

    /**
     * Handle search form submission
     */
    async handleSearch(e) {
        e.preventDefault();

        const city = this.cityInput.value.trim();
        if (!city) {
            this.showError('Please enter a city name');
            return;
        }

        if (this.API_KEY === 'YOUR_API_KEY_HERE') {
            this.showError('Please add your OpenWeatherMap API key to the script.js file');
            return;
        }

        this.lastSearchedCity = city;
        await this.fetchWeatherData(city);
    }

    /**
     * Retry the last search
     */
    async retryLastSearch() {
        if (this.lastSearchedCity) {
            await this.fetchWeatherData(this.lastSearchedCity);
        }
    }

    /**
     * Load user preferences from localStorage
     */
    loadPreference(key, defaultValue) {
        try {
            const value = localStorage.getItem(`weatherApp_${key}`);
            return value !== null ? value : defaultValue;
        } catch (error) {
            console.warn('Failed to load preference:', key, error);
            return defaultValue;
        }
    }

    /**
     * Save user preference to localStorage
     */
    savePreference(key, value) {
        try {
            localStorage.setItem(`weatherApp_${key}`, value);
        } catch (error) {
            console.warn('Failed to save preference:', key, error);
        }
    }

    /**
     * Load search history from localStorage
     */
    loadSearchHistory() {
        try {
            const history = localStorage.getItem('weatherApp_searchHistory');
            return history ? JSON.parse(history) : [];
        } catch (error) {
            console.warn('Failed to load search history:', error);
            return [];
        }
    }

    /**
     * Save search history to localStorage
     */
    saveSearchHistory() {
        try {
            localStorage.setItem('weatherApp_searchHistory', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.warn('Failed to save search history:', error);
        }
    }

    /**
     * Fetch current weather and forecast data
     */
    async fetchWeatherData(city) {
        this.showLoading();
        this.hideError();
        this.hideWeather();
        this.hideForecast();

        try {
            // Fetch both current weather and forecast data
            const [weatherResponse, forecastResponse] = await Promise.all([
                fetch(`${this.BASE_URL}?q=${encodeURIComponent(city)}&appid=${this.API_KEY}&units=metric`),
                fetch(`${this.FORECAST_URL}?q=${encodeURIComponent(city)}&appid=${this.API_KEY}&units=metric`)
            ]);

            if (!weatherResponse.ok) {
                if (weatherResponse.status === 404) {
                    throw new Error('City not found. Please check the spelling and try again.');
                } else if (weatherResponse.status === 401) {
                    throw new Error('Invalid API key. Please check your OpenWeatherMap API key.');
                } else {
                    throw new Error('Failed to fetch weather data. Please try again later.');
                }
            }

            const weatherData = await weatherResponse.json();
            const forecastData = await forecastResponse.json();

            this.currentWeatherData = weatherData;
            this.forecastData = forecastData;

            // Display the data
            this.displayWeatherData(weatherData);
            this.displayForecastData(forecastData);

            // Add to search history
            this.addToSearchHistory(city, weatherData.name, weatherData.sys.country);

            // Clear input after successful search
            this.cityInput.value = '';

        } catch (error) {
            console.error('Error fetching weather data:', error);
            this.showError(error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Fetch weather data by coordinates (for geolocation)
     */
    async fetchWeatherByCoords(lat, lon) {
        this.showLoading();
        this.hideError();
        this.hideWeather();
        this.hideForecast();

        try {
            const [weatherResponse, forecastResponse] = await Promise.all([
                fetch(`${this.BASE_URL}?lat=${lat}&lon=${lon}&appid=${this.API_KEY}&units=metric`),
                fetch(`${this.FORECAST_URL}?lat=${lat}&lon=${lon}&appid=${this.API_KEY}&units=metric`)
            ]);

            if (!weatherResponse.ok) {
                throw new Error('Failed to fetch weather data for your location.');
            }

            const weatherData = await weatherResponse.json();
            const forecastData = await forecastResponse.json();

            this.currentWeatherData = weatherData;
            this.forecastData = forecastData;

            this.displayWeatherData(weatherData);
            this.displayForecastData(forecastData);

            this.addToSearchHistory('Current Location', weatherData.name, weatherData.sys.country);

        } catch (error) {
            console.error('Error fetching location weather:', error);
            this.showError(error.message);
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Display current weather data
     */
    displayWeatherData(data) {
        // Update city name and date
        this.cityName.textContent = `${data.name}, ${data.sys.country}`;
        this.updateDateTime();

        // Update temperature
        this.updateTemperatureDisplay(data);

        // Update weather icon and condition with animation
        const iconCode = data.weather[0].icon;
        this.weatherIcon.src = `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
        this.weatherIcon.alt = data.weather[0].description;
        this.weatherIcon.classList.add('weather-icon-animated');
        this.weatherCondition.textContent = data.weather[0].description;

        // Update weather details
        this.humidity.textContent = `${data.main.humidity}%`;
        this.windSpeed.textContent = `${Math.round(data.wind.speed * 3.6)} km/h`;
        this.visibility.textContent = data.visibility ? `${(data.visibility / 1000).toFixed(1)} km` : 'N/A';
        this.pressure.textContent = `${data.main.pressure} hPa`;

        this.showWeather();
    }

    /**
     * Display 5-day forecast data
     */
    displayForecastData(data) {
        if (!data || !data.list) return;

        // Clear existing forecast
        this.forecastContainer.innerHTML = '';

        // Get daily forecasts (every 8th item = 24 hours apart)
        const dailyForecasts = [];
        for (let i = 0; i < data.list.length; i += 8) {
            if (dailyForecasts.length >= 5) break;
            dailyForecasts.push(data.list[i]);
        }

        // Create forecast cards
        dailyForecasts.forEach((forecast, index) => {
            const forecastCard = this.createForecastCard(forecast, index === 0);
            this.forecastContainer.appendChild(forecastCard);
        });

        this.showForecast();
    }

    /**
     * Create a forecast card element
     */
    createForecastCard(forecast, isToday = false) {
        const card = document.createElement('div');
        card.className = 'forecast-card';

        const date = new Date(forecast.dt * 1000);
        const dayName = isToday ? 'Today' : date.toLocaleDateString('en-US', { weekday: 'short' });

        const tempMin = Math.round(forecast.main.temp_min);
        const tempMax = Math.round(forecast.main.temp_max);

        const iconCode = forecast.weather[0].icon;
        const description = forecast.weather[0].description;

        card.innerHTML = `
            <div class="forecast-day">${dayName}</div>
            <img class="forecast-icon" src="https://openweathermap.org/img/wn/${iconCode}@2x.png" alt="${description}">
            <div class="forecast-temps">
                <span class="forecast-high">${this.formatTemperature(tempMax)}°</span>
                <span class="forecast-low">${this.formatTemperature(tempMin)}°</span>
            </div>
            <div class="forecast-desc">${description}</div>
        `;

        return card;
    }

    /**
     * Format temperature based on current unit
     */
    formatTemperature(tempCelsius) {
        if (this.currentUnit === 'fahrenheit') {
            return Math.round((tempCelsius * 9/5) + 32);
        }
        return Math.round(tempCelsius);
    }

    /**
     * Update temperature display based on current unit
     */
    updateTemperatureDisplay(data) {
        const temp = Math.round(data.main.temp);
        const feelsLikeTemp = Math.round(data.main.feels_like);

        if (this.currentUnit === 'celsius') {
            this.temperature.textContent = `${temp}°`;
            this.feelsLike.textContent = `${feelsLikeTemp}°C`;
        } else {
            const tempF = Math.round((temp * 9/5) + 32);
            const feelsLikeTempF = Math.round((feelsLikeTemp * 9/5) + 32);
            this.temperature.textContent = `${tempF}°`;
            this.feelsLike.textContent = `${feelsLikeTempF}°F`;
        }
    }

    /**
     * Toggle temperature unit and update display
     */
    toggleUnit(unit) {
        if (unit === this.currentUnit) return;

        this.currentUnit = unit;
        this.savePreference('temperatureUnit', unit);

        // Update button states
        this.celsiusBtn.classList.toggle('active', unit === 'celsius');
        this.fahrenheitBtn.classList.toggle('active', unit === 'fahrenheit');

        // Update temperature display if weather data exists
        if (this.currentWeatherData) {
            this.updateTemperatureDisplay(this.currentWeatherData);
        }

        // Update forecast display
        if (this.forecastData) {
            this.displayForecastData(this.forecastData);
        }
    }

    /**
     * Initialize temperature unit from saved preference
     */
    initializeTemperatureUnit() {
        this.celsiusBtn.classList.toggle('active', this.currentUnit === 'celsius');
        this.fahrenheitBtn.classList.toggle('active', this.currentUnit === 'fahrenheit');
    }

    /**
     * Toggle between light and dark themes
     */
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.savePreference('theme', this.currentTheme);
        this.applyTheme();
    }

    /**
     * Initialize theme from saved preference
     */
    initializeTheme() {
        this.applyTheme();
    }

    /**
     * Apply the current theme
     */
    applyTheme() {
        document.body.setAttribute('data-theme', this.currentTheme);
        const icon = this.themeToggle.querySelector('i');

        if (this.currentTheme === 'dark') {
            icon.className = 'fas fa-sun';
            this.themeToggle.title = 'Switch to light mode';
        } else {
            icon.className = 'fas fa-moon';
            this.themeToggle.title = 'Switch to dark mode';
        }
    }

    /**
     * Request user's current location weather
     */
    requestLocationWeather() {
        if (!navigator.geolocation) {
            this.showError('Geolocation is not supported by this browser.');
            return;
        }

        this.locationBtn.disabled = true;
        this.locationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

        navigator.geolocation.getCurrentPosition(
            (position) => {
                const { latitude, longitude } = position.coords;
                this.fetchWeatherByCoords(latitude, longitude);
                this.locationBtn.disabled = false;
                this.locationBtn.innerHTML = '<i class="fas fa-location-dot"></i>';
            },
            (error) => {
                console.error('Geolocation error:', error);
                let errorMessage = 'Unable to get your location. ';

                switch (error.code) {
                    case error.PERMISSION_DENIED:
                        errorMessage += 'Please allow location access.';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        errorMessage += 'Location information unavailable.';
                        break;
                    case error.TIMEOUT:
                        errorMessage += 'Location request timed out.';
                        break;
                    default:
                        errorMessage += 'An unknown error occurred.';
                        break;
                }

                this.showError(errorMessage);
                this.locationBtn.disabled = false;
                this.locationBtn.innerHTML = '<i class="fas fa-location-dot"></i>';
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 300000 // 5 minutes
            }
        );
    }

    /**
     * Add city to search history
     */
    addToSearchHistory(searchTerm, cityName, country) {
        const historyItem = {
            searchTerm,
            displayName: `${cityName}, ${country}`,
            timestamp: Date.now()
        };

        // Remove existing entry if it exists
        this.searchHistory = this.searchHistory.filter(item =>
            item.displayName !== historyItem.displayName
        );

        // Add to beginning of array
        this.searchHistory.unshift(historyItem);

        // Keep only last 10 searches
        this.searchHistory = this.searchHistory.slice(0, 10);

        this.saveSearchHistory();
        this.displaySearchHistory();
    }

    /**
     * Display search history
     */
    displaySearchHistory() {
        if (this.searchHistory.length === 0) {
            this.searchHistory.classList.remove('show');
            return;
        }

        this.historyItems.innerHTML = '';

        this.searchHistory.forEach(item => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            historyItem.textContent = item.displayName;
            historyItem.addEventListener('click', () => {
                this.cityInput.value = item.searchTerm;
                this.handleSearch(new Event('submit'));
            });
            this.historyItems.appendChild(historyItem);
        });

        this.searchHistory.classList.add('show');
    }

    /**
     * Clear search history
     */
    clearSearchHistory() {
        this.searchHistory = [];
        this.saveSearchHistory();
        this.displaySearchHistory();
    }

    /**
     * Show search suggestions (placeholder for future enhancement)
     */
    showSearchSuggestions() {
        // This could be enhanced with a city suggestions API
        // For now, just show search history when user starts typing
        if (this.cityInput.value.length > 0 && this.searchHistory.length > 0) {
            this.displaySearchHistory();
        }
    }

    /**
     * Update current date and time display
     */
    updateDateTime() {
        const now = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        this.currentDate.textContent = now.toLocaleDateString('en-US', options);
    }

    /**
     * Debounce function to limit API calls
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // UI State Management Methods

    /**
     * Show loading indicator
     */
    showLoading() {
        this.loading.classList.add('show');
        this.searchBtn.disabled = true;
    }

    /**
     * Hide loading indicator
     */
    hideLoading() {
        this.loading.classList.remove('show');
        this.searchBtn.disabled = false;
    }

    /**
     * Show error message
     */
    showError(message) {
        this.errorText.textContent = message;
        this.errorMessage.classList.add('show');
    }

    /**
     * Hide error message
     */
    hideError() {
        this.errorMessage.classList.remove('show');
    }

    /**
     * Show weather section
     */
    showWeather() {
        this.weatherSection.classList.add('show');
    }

    /**
     * Hide weather section
     */
    hideWeather() {
        this.weatherSection.classList.remove('show');
    }

    /**
     * Show forecast section
     */
    showForecast() {
        this.forecastSection.classList.add('show');
    }

    /**
     * Hide forecast section
     */
    hideForecast() {
        this.forecastSection.classList.remove('show');
    }
}

/**
 * Initialize the Enhanced Weather App when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
    new EnhancedWeatherApp();
});

/**
 * Additional utility functions and enhancements
 */

// Add smooth scroll animations when elements come into view
function addScrollAnimations() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    // Observe weather cards and sections
    document.querySelectorAll('.weather-card, .search-section, .forecast-section').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + K to focus search
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        document.getElementById('cityInput').focus();
    }

    // Escape to clear search and hide history
    if (e.key === 'Escape') {
        const cityInput = document.getElementById('cityInput');
        const searchHistory = document.getElementById('searchHistory');
        cityInput.blur();
        cityInput.value = '';
        searchHistory.classList.remove('show');
    }
});

// Add service worker for offline functionality (optional enhancement)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        // Service worker registration would go here
        // This is a placeholder for future offline functionality
    });
}

// Initialize scroll animations after DOM load
document.addEventListener('DOMContentLoaded', addScrollAnimations);

// Add error handling for uncaught errors
window.addEventListener('error', (e) => {
    console.error('Uncaught error:', e.error);
});

window.addEventListener('unhandledrejection', (e) => {
    console.error('Unhandled promise rejection:', e.reason);
});

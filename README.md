# Weather App

A responsive weather application built with HTML, CSS, and vanilla JavaScript that provides real-time weather information using the OpenWeatherMap API.

## Features

- 🌤️ **Real-time Weather Data**: Get current weather information for any city worldwide
- 📱 **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- 🌡️ **Temperature Units**: Switch between Celsius and Fahrenheit
- 🎨 **Beautiful UI**: Modern, clean interface with smooth animations
- ⚡ **Fast Loading**: Optimized performance with loading indicators
- 🚨 **Error Handling**: User-friendly error messages for invalid cities or network issues
- 🌪️ **Comprehensive Data**: Displays temperature, weather condition, humidity, wind speed, visibility, and pressure

## Weather Information Displayed

- City name and country
- Current temperature (with feels-like temperature)
- Weather condition and icon
- Humidity percentage
- Wind speed (in km/h)
- Visibility (in km)
- Atmospheric pressure (in hPa)
- Current date and time

## Setup Instructions

### 1. Get OpenWeatherMap API Key

1. Visit [OpenWeatherMap](https://openweathermap.org/api)
2. Sign up for a free account
3. Go to your API keys section
4. Copy your API key

### 2. Configure the Application

1. Open `script.js` file
2. Find the line: `this.API_KEY = 'YOUR_API_KEY_HERE';`
3. Replace `'YOUR_API_KEY_HERE'` with your actual API key from OpenWeatherMap

Example:
```javascript
this.API_KEY = 'abcd1234efgh5678ijkl9012mnop3456';
```

### 3. Run the Application

1. Open `index.html` in your web browser
2. Enter a city name in the search box
3. Press Enter or click the search button
4. View the weather information!

## File Structure

```
Weather App/
├── index.html          # Main HTML structure
├── styles.css          # CSS styling and responsive design
├── script.js           # JavaScript functionality and API integration
└── README.md          # This file
```

## Technologies Used

- **HTML5**: Semantic markup and structure
- **CSS3**: Styling, animations, and responsive design
- **Vanilla JavaScript**: API calls, DOM manipulation, and user interactions
- **OpenWeatherMap API**: Real-time weather data
- **Font Awesome**: Icons for enhanced UI

## Browser Compatibility

- Chrome (recommended)
- Firefox
- Safari
- Edge
- Mobile browsers

## API Rate Limits

The free OpenWeatherMap API plan includes:
- 1,000 API calls per day
- 60 API calls per minute

## Troubleshooting

### Common Issues

1. **"Please add your OpenWeatherMap API key"**
   - Make sure you've replaced `YOUR_API_KEY_HERE` with your actual API key in `script.js`

2. **"City not found"**
   - Check the spelling of the city name
   - Try using the full city name or include the country (e.g., "London, UK")

3. **"Invalid API key"**
   - Verify your API key is correct
   - Make sure your API key is activated (can take a few minutes after signup)

4. **Network errors**
   - Check your internet connection
   - Try refreshing the page

## Features in Detail

### Responsive Design
- Mobile-first approach
- Flexible grid layout
- Touch-friendly interface
- Optimized for all screen sizes

### User Experience
- Smooth animations and transitions
- Loading indicators during API calls
- Clear error messages
- Intuitive interface design

### Performance
- Efficient API calls
- Minimal dependencies
- Fast loading times
- Optimized images and icons

## Future Enhancements

Potential features that could be added:
- 5-day weather forecast
- Geolocation-based weather
- Weather alerts and notifications
- Multiple city comparison
- Weather history and trends
- Dark/light theme toggle

## License

This project is open source and available under the [MIT License](LICENSE).

## Contributing

Feel free to fork this project and submit pull requests for any improvements.

## Support

If you encounter any issues or have questions, please check the troubleshooting section above or create an issue in the project repository.

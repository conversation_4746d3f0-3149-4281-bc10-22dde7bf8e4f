/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables for Theme Support */
:root {
    /* Light Theme Colors */
    --primary-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-bg: rgba(255, 255, 255, 0.95);
    --card-bg: rgba(255, 255, 255, 0.98);
    --text-primary: #2d3436;
    --text-secondary: #636e72;
    --text-light: rgba(255, 255, 255, 0.9);
    --accent-color: #6c5ce7;
    --accent-hover: #5f3dc4;
    --success-color: #00b894;
    --error-color: #e17055;
    --warning-color: #fdcb6e;
    --border-color: rgba(255, 255, 255, 0.2);
    --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 12px 40px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.1);
    --backdrop-blur: blur(10px);
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --primary-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --secondary-bg: rgba(44, 62, 80, 0.95);
    --card-bg: rgba(52, 73, 94, 0.98);
    --text-primary: #ecf0f1;
    --text-secondary: #bdc3c7;
    --text-light: rgba(236, 240, 241, 0.9);
    --accent-color: #9b59b6;
    --accent-hover: #8e44ad;
    --success-color: #27ae60;
    --error-color: #e74c3c;
    --warning-color: #f39c12;
    --border-color: rgba(236, 240, 241, 0.1);
    --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 12px 40px rgba(0, 0, 0, 0.4);
    --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.3);
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--primary-bg);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
    transition: all 0.3s ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-light);
}

.header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.weather-icon-animated {
    color: var(--warning-color);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.header-controls {
    display: flex;
    gap: 0.5rem;
}

.theme-toggle,
.location-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--text-light);
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
}

.theme-toggle:hover,
.location-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Main Content */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

/* Welcome Section */
.welcome-section {
    width: 100%;
    max-width: 900px;
    margin-bottom: 1rem;
    animation: fadeInUp 0.8s ease;
}

.welcome-section.hidden {
    display: none;
}

.welcome-content {
    background: var(--secondary-bg);
    backdrop-filter: var(--backdrop-blur);
    border-radius: 25px;
    padding: 2.5rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-heavy);
    text-align: center;
}

.welcome-message {
    margin-bottom: 2rem;
}

.welcome-title {
    font-size: 2.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.weather-pulse {
    color: var(--warning-color);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

.welcome-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
    font-weight: 400;
    line-height: 1.5;
}

/* Live Date and Time */
.live-datetime {
    background: var(--card-bg);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
}

.current-time {
    font-size: 2.5rem;
    font-weight: 300;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
    font-family: 'Courier New', monospace;
    letter-spacing: 2px;
}

.current-date {
    font-size: 1.1rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Weather Illustration */
.weather-illustration {
    margin: 2rem 0;
    height: 200px;
    position: relative;
    overflow: hidden;
}

.weather-scene {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sun {
    position: absolute;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, #ffd700 0%, #ffed4e 70%);
    border-radius: 50%;
    top: 20px;
    right: 20%;
    animation: rotate 20s linear infinite;
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
}

.sun-rays {
    position: absolute;
    width: 120px;
    height: 120px;
    top: -20px;
    left: -20px;
    background: conic-gradient(
        transparent 0deg,
        rgba(255, 215, 0, 0.3) 10deg,
        transparent 20deg,
        rgba(255, 215, 0, 0.3) 30deg,
        transparent 40deg,
        rgba(255, 215, 0, 0.3) 50deg,
        transparent 60deg,
        rgba(255, 215, 0, 0.3) 70deg,
        transparent 80deg
    );
    border-radius: 50%;
    animation: rotate 30s linear infinite reverse;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.clouds {
    position: absolute;
    width: 100%;
    height: 100%;
}

.cloud {
    position: absolute;
    background: var(--text-light);
    border-radius: 50px;
    opacity: 0.8;
}

.cloud::before,
.cloud::after {
    content: '';
    position: absolute;
    background: var(--text-light);
    border-radius: 50px;
}

.cloud-1 {
    width: 60px;
    height: 30px;
    top: 60px;
    left: 10%;
    animation: float 6s ease-in-out infinite;
}

.cloud-1::before {
    width: 40px;
    height: 40px;
    top: -20px;
    left: 5px;
}

.cloud-1::after {
    width: 30px;
    height: 30px;
    top: -15px;
    right: 5px;
}

.cloud-2 {
    width: 80px;
    height: 40px;
    top: 40px;
    left: 60%;
    animation: float 8s ease-in-out infinite reverse;
}

.cloud-2::before {
    width: 50px;
    height: 50px;
    top: -25px;
    left: 10px;
}

.cloud-2::after {
    width: 40px;
    height: 40px;
    top: -20px;
    right: 10px;
}

.cloud-3 {
    width: 50px;
    height: 25px;
    top: 100px;
    left: 30%;
    animation: float 7s ease-in-out infinite;
}

.cloud-3::before {
    width: 35px;
    height: 35px;
    top: -18px;
    left: 5px;
}

.cloud-3::after {
    width: 25px;
    height: 25px;
    top: -12px;
    right: 5px;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) translateX(0px); }
    25% { transform: translateY(-10px) translateX(5px); }
    50% { transform: translateY(0px) translateX(10px); }
    75% { transform: translateY(-5px) translateX(5px); }
}

.rain-drops {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.rain-drops.active {
    opacity: 1;
}

.rain-drop {
    position: absolute;
    width: 2px;
    height: 15px;
    background: linear-gradient(to bottom, transparent, var(--accent-color));
    border-radius: 0 0 2px 2px;
    animation: fall 1s linear infinite;
}

@keyframes fall {
    from {
        transform: translateY(-20px);
        opacity: 1;
    }
    to {
        transform: translateY(200px);
        opacity: 0;
    }
}

/* Example Cities */
.example-cities {
    margin: 2rem 0;
}

.example-title {
    font-size: 1rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
    font-weight: 500;
}

.city-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
}

.city-suggestion {
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 25px;
    padding: 0.75rem 1.25rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.city-suggestion::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(108, 92, 231, 0.1), transparent);
    transition: left 0.5s ease;
}

.city-suggestion:hover::before {
    left: 100%;
}

.city-suggestion:hover {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(108, 92, 231, 0.3);
}

.city-suggestion i {
    font-size: 0.8rem;
    opacity: 0.8;
}

/* Weather Fact */
.weather-fact {
    background: var(--card-bg);
    border-radius: 15px;
    padding: 1.5rem;
    margin: 2rem 0;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    text-align: left;
    position: relative;
    overflow: hidden;
}

.weather-fact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--accent-color);
}

.fact-icon {
    background: var(--accent-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    animation: glow 3s ease-in-out infinite;
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 10px rgba(108, 92, 231, 0.3); }
    50% { box-shadow: 0 0 20px rgba(108, 92, 231, 0.6); }
}

.fact-content {
    flex: 1;
}

.fact-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.fact-text {
    font-size: 0.9rem;
    color: var(--text-secondary);
    line-height: 1.5;
    font-style: italic;
}

/* Quick Stats */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 2rem;
}

.stat-item {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--accent-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.stat-item:hover::before {
    transform: scaleX(1);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.stat-item i {
    font-size: 1.5rem;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Smart Features */
.smart-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.feature-item {
    background: var(--card-bg);
    border-radius: 15px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(108, 92, 231, 0.05), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.feature-item:hover::before {
    transform: translateX(100%);
}

.feature-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.feature-icon {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-hover));
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 1.2rem;
}

.feature-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.feature-content p {
    font-size: 0.85rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* Weather Insights */
.weather-insights {
    background: var(--card-bg);
    border-radius: 15px;
    padding: 1.5rem;
    margin-top: 2rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-light);
}

.insight-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.insight-header i {
    color: var(--accent-color);
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
}

.insight-item {
    text-align: center;
    padding: 1rem;
    background: rgba(108, 92, 231, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(108, 92, 231, 0.1);
    transition: all 0.3s ease;
}

.insight-item:hover {
    background: rgba(108, 92, 231, 0.1);
    transform: translateY(-2px);
}

.insight-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.25rem;
}

.insight-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* AQI Color Coding */
.aqi-good { color: #00e676 !important; }
.aqi-moderate { color: #ffeb3b !important; }
.aqi-fair { color: #ff9800 !important; }
.aqi-poor { color: #f44336 !important; }

/* Search Section */
.search-section {
    width: 100%;
    max-width: 500px;
}

.search-form {
    width: 100%;
}

.search-input-container {
    display: flex;
    background: var(--card-bg);
    border-radius: 50px;
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
}

.search-input-container:focus-within {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.search-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    outline: none;
    font-size: 1rem;
    background: transparent;
    color: var(--text-primary);
}

.search-input::placeholder {
    color: var(--text-secondary);
}

.search-btn {
    padding: 1rem 1.5rem;
    background: var(--accent-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.search-btn:hover {
    background: var(--accent-hover);
}

.search-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Search History Styles */
.search-history {
    margin-top: 1rem;
    background: var(--secondary-bg);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
    display: none;
}

.search-history.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.history-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
}

.clear-history-btn {
    background: none;
    border: none;
    color: var(--error-color);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.clear-history-btn:hover {
    background: rgba(225, 112, 85, 0.1);
}

.history-items {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.history-item {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    color: var(--text-primary);
}

.history-item:hover {
    background: var(--accent-color);
    color: white;
    transform: translateY(-2px);
}

/* Enhanced Loading Indicator */
.loading {
    display: none;
    text-align: center;
    color: var(--text-light);
    margin: 2rem 0;
}

.loading.show {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.loading-content {
    background: var(--secondary-bg);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
    max-width: 300px;
    margin: 0 auto;
}

.spinner-container {
    position: relative;
    margin-bottom: 1rem;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(108, 92, 231, 0.3);
    border-top: 4px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.shimmer-effect {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(108, 92, 231, 0.2),
        transparent
    );
    border-radius: 50%;
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.loading-text {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
}

/* Enhanced Error Message */
.error-message {
    display: none;
    background: var(--error-color);
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(225, 112, 85, 0.3);
    max-width: 500px;
    width: 100%;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.error-message.show {
    display: block;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-message i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.retry-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 1rem;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.retry-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.retry-btn i {
    margin-right: 0.5rem;
}

/* Weather Section */
.weather-section {
    display: none;
    width: 100%;
    max-width: 600px;
}

.weather-section.show {
    display: block;
}

.weather-card {
    background: var(--card-bg);
    backdrop-filter: var(--backdrop-blur);
    border-radius: 25px;
    padding: 2rem;
    box-shadow: var(--shadow-heavy);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.weather-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 70px rgba(0, 0, 0, 0.15);
}

/* Weather Header */
.weather-header {
    text-align: center;
    margin-bottom: 2rem;
}

.city-name {
    font-size: 2rem;
    font-weight: 600;
    color: #2d3436;
    margin-bottom: 0.5rem;
}

.date {
    color: #636e72;
    font-size: 1rem;
}

/* Main Weather Info */
.weather-main {
    margin-bottom: 2rem;
}

.temperature-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.temperature-display {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.temperature {
    font-size: 4rem;
    font-weight: 300;
    color: #2d3436;
}

.unit-toggle {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.unit-btn {
    padding: 0.25rem 0.5rem;
    border: 2px solid #ddd;
    background: white;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.unit-btn.active {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.weather-icon-container {
    text-align: center;
}

.weather-icon {
    width: 80px;
    height: 80px;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.weather-description {
    text-align: center;
}

.condition {
    font-size: 1.5rem;
    font-weight: 500;
    color: #2d3436;
    text-transform: capitalize;
    margin-bottom: 0.5rem;
}

.feels-like {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Weather Mood */
.weather-mood {
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1), rgba(108, 92, 231, 0.05));
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid rgba(108, 92, 231, 0.2);
    animation: moodPulse 3s ease-in-out infinite;
}

@keyframes moodPulse {
    0%, 100% { box-shadow: 0 0 20px rgba(108, 92, 231, 0.1); }
    50% { box-shadow: 0 0 30px rgba(108, 92, 231, 0.2); }
}

.mood-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mood-emoji {
    font-size: 3rem;
    animation: bounce 2s ease-in-out infinite;
}

.mood-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mood-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.mood-activity {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-style: italic;
}

/* Weather Details */
.weather-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
    padding-top: 2rem;
    border-top: 1px solid #e9ecef;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: transform 0.2s ease;
}

.detail-item:hover {
    transform: translateY(-2px);
}

.detail-item i {
    font-size: 1.2rem;
    color: var(--accent-color);
    width: 20px;
    text-align: center;
}

.detail-info {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 0.85rem;
    color: #636e72;
    font-weight: 500;
}

.detail-value {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3436;
}

/* 5-Day Forecast Styles */
.forecast-section {
    display: none;
    width: 100%;
    max-width: 800px;
    margin-top: 2rem;
}

.forecast-section.show {
    display: block;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.forecast-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.forecast-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-light);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.forecast-title i {
    margin-right: 0.5rem;
    color: var(--warning-color);
}

.forecast-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
}

.forecast-card {
    background: var(--card-bg);
    backdrop-filter: var(--backdrop-blur);
    border-radius: 20px;
    padding: 1.5rem 1rem;
    text-align: center;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-light);
}

.forecast-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.forecast-day {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.forecast-icon {
    width: 50px;
    height: 50px;
    margin: 0.5rem auto;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.forecast-temps {
    margin-top: 0.5rem;
}

.forecast-high {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.forecast-low {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-left: 0.5rem;
}

.forecast-desc {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    text-transform: capitalize;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1rem;
    color: var(--text-light);
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header-top {
        flex-direction: column;
        gap: 1rem;
    }

    .title {
        font-size: 2rem;
    }

    /* Welcome Section Mobile */
    .welcome-content {
        padding: 1.5rem;
    }

    .welcome-title {
        font-size: 1.8rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .current-time {
        font-size: 2rem;
    }

    .weather-illustration {
        height: 150px;
    }

    .sun {
        width: 60px;
        height: 60px;
        top: 15px;
    }

    .city-suggestions {
        gap: 0.5rem;
    }

    .city-suggestion {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .weather-fact {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .quick-stats {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .smart-features {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }

    .insights-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .weather-card {
        padding: 1.5rem;
    }

    .temperature-section {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .temperature {
        font-size: 3rem;
    }

    .weather-details {
        grid-template-columns: 1fr;
    }

    .city-name {
        font-size: 1.5rem;
    }

    .forecast-container {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.75rem;
    }

    .forecast-card {
        padding: 1rem 0.75rem;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 1.8rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    /* Welcome Section Small Mobile */
    .welcome-content {
        padding: 1rem;
    }

    .welcome-title {
        font-size: 1.5rem;
    }

    .current-time {
        font-size: 1.8rem;
    }

    .weather-illustration {
        height: 120px;
    }

    .city-suggestions {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .city-suggestion {
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
        justify-content: center;
    }

    .quick-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }

    .stat-item {
        padding: 0.75rem 0.5rem;
    }

    .stat-number {
        font-size: 1.2rem;
    }

    .smart-features {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .feature-item {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
    }

    .insights-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }

    .insight-item {
        padding: 0.75rem 0.5rem;
    }

    .insight-value {
        font-size: 1.2rem;
    }

    .weather-card {
        padding: 1rem;
    }

    .temperature {
        font-size: 2.5rem;
    }

    .search-input, .search-btn {
        padding: 0.75rem 1rem;
    }

    .forecast-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .history-items {
        justify-content: center;
    }
}

/* Weather Icon Animations */
.weather-icon {
    transition: transform 0.3s ease;
}

.weather-icon:hover {
    transform: scale(1.1) rotate(5deg);
}

/* Animated weather conditions */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.weather-icon-animated {
    animation: bounce 2s infinite;
}

/* Smooth transitions for theme switching */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-bg);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--accent-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-hover);
}

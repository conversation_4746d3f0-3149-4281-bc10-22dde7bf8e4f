/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables for Theme Support */
:root {
    /* Light Theme Colors */
    --primary-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-bg: rgba(255, 255, 255, 0.95);
    --card-bg: rgba(255, 255, 255, 0.98);
    --text-primary: #2d3436;
    --text-secondary: #636e72;
    --text-light: rgba(255, 255, 255, 0.9);
    --accent-color: #6c5ce7;
    --accent-hover: #5f3dc4;
    --success-color: #00b894;
    --error-color: #e17055;
    --warning-color: #fdcb6e;
    --border-color: rgba(255, 255, 255, 0.2);
    --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 12px 40px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.1);
    --backdrop-blur: blur(10px);
}

/* Dark Theme Colors */
[data-theme="dark"] {
    --primary-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    --secondary-bg: rgba(44, 62, 80, 0.95);
    --card-bg: rgba(52, 73, 94, 0.98);
    --text-primary: #ecf0f1;
    --text-secondary: #bdc3c7;
    --text-light: rgba(236, 240, 241, 0.9);
    --accent-color: #9b59b6;
    --accent-hover: #8e44ad;
    --success-color: #27ae60;
    --error-color: #e74c3c;
    --warning-color: #f39c12;
    --border-color: rgba(236, 240, 241, 0.1);
    --shadow-light: 0 8px 32px rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 12px 40px rgba(0, 0, 0, 0.4);
    --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.3);
}

body {
    font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--primary-bg);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
    transition: all 0.3s ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-light);
}

.header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.weather-icon-animated {
    color: var(--warning-color);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.header-controls {
    display: flex;
    gap: 0.5rem;
}

.theme-toggle,
.location-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: var(--text-light);
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
}

.theme-toggle:hover,
.location-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Main Content */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

/* Search Section */
.search-section {
    width: 100%;
    max-width: 500px;
}

.search-form {
    width: 100%;
}

.search-input-container {
    display: flex;
    background: var(--card-bg);
    border-radius: 50px;
    box-shadow: var(--shadow-light);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
}

.search-input-container:focus-within {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.search-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    outline: none;
    font-size: 1rem;
    background: transparent;
    color: var(--text-primary);
}

.search-input::placeholder {
    color: var(--text-secondary);
}

.search-btn {
    padding: 1rem 1.5rem;
    background: var(--accent-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.search-btn:hover {
    background: var(--accent-hover);
}

.search-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Search History Styles */
.search-history {
    margin-top: 1rem;
    background: var(--secondary-bg);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
    display: none;
}

.search-history.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.history-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
}

.clear-history-btn {
    background: none;
    border: none;
    color: var(--error-color);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.clear-history-btn:hover {
    background: rgba(225, 112, 85, 0.1);
}

.history-items {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.history-item {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    color: var(--text-primary);
}

.history-item:hover {
    background: var(--accent-color);
    color: white;
    transform: translateY(-2px);
}

/* Enhanced Loading Indicator */
.loading {
    display: none;
    text-align: center;
    color: var(--text-light);
    margin: 2rem 0;
}

.loading.show {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.loading-content {
    background: var(--secondary-bg);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
    max-width: 300px;
    margin: 0 auto;
}

.spinner-container {
    position: relative;
    margin-bottom: 1rem;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(108, 92, 231, 0.3);
    border-top: 4px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.shimmer-effect {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(108, 92, 231, 0.2),
        transparent
    );
    border-radius: 50%;
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.loading-text {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
}

/* Enhanced Error Message */
.error-message {
    display: none;
    background: var(--error-color);
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(225, 112, 85, 0.3);
    max-width: 500px;
    width: 100%;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.error-message.show {
    display: block;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-message i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.retry-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 1rem;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.retry-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.retry-btn i {
    margin-right: 0.5rem;
}

/* Weather Section */
.weather-section {
    display: none;
    width: 100%;
    max-width: 600px;
}

.weather-section.show {
    display: block;
}

.weather-card {
    background: var(--card-bg);
    backdrop-filter: var(--backdrop-blur);
    border-radius: 25px;
    padding: 2rem;
    box-shadow: var(--shadow-heavy);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.weather-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 70px rgba(0, 0, 0, 0.15);
}

/* Weather Header */
.weather-header {
    text-align: center;
    margin-bottom: 2rem;
}

.city-name {
    font-size: 2rem;
    font-weight: 600;
    color: #2d3436;
    margin-bottom: 0.5rem;
}

.date {
    color: #636e72;
    font-size: 1rem;
}

/* Main Weather Info */
.weather-main {
    margin-bottom: 2rem;
}

.temperature-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.temperature-display {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.temperature {
    font-size: 4rem;
    font-weight: 300;
    color: #2d3436;
}

.unit-toggle {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.unit-btn {
    padding: 0.25rem 0.5rem;
    border: 2px solid #ddd;
    background: white;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.unit-btn.active {
    background: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
}

.weather-icon-container {
    text-align: center;
}

.weather-icon {
    width: 80px;
    height: 80px;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.weather-description {
    text-align: center;
}

.condition {
    font-size: 1.5rem;
    font-weight: 500;
    color: #2d3436;
    text-transform: capitalize;
    margin-bottom: 0.5rem;
}

.feels-like {
    color: #636e72;
    font-size: 1rem;
}

/* Weather Details */
.weather-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
    padding-top: 2rem;
    border-top: 1px solid #e9ecef;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: transform 0.2s ease;
}

.detail-item:hover {
    transform: translateY(-2px);
}

.detail-item i {
    font-size: 1.2rem;
    color: var(--accent-color);
    width: 20px;
    text-align: center;
}

.detail-info {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 0.85rem;
    color: #636e72;
    font-weight: 500;
}

.detail-value {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3436;
}

/* 5-Day Forecast Styles */
.forecast-section {
    display: none;
    width: 100%;
    max-width: 800px;
    margin-top: 2rem;
}

.forecast-section.show {
    display: block;
    animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.forecast-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.forecast-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-light);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.forecast-title i {
    margin-right: 0.5rem;
    color: var(--warning-color);
}

.forecast-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
}

.forecast-card {
    background: var(--card-bg);
    backdrop-filter: var(--backdrop-blur);
    border-radius: 20px;
    padding: 1.5rem 1rem;
    text-align: center;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-light);
}

.forecast-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.forecast-day {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.forecast-icon {
    width: 50px;
    height: 50px;
    margin: 0.5rem auto;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.forecast-temps {
    margin-top: 0.5rem;
}

.forecast-high {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.forecast-low {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-left: 0.5rem;
}

.forecast-desc {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
    text-transform: capitalize;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1rem;
    color: var(--text-light);
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header-top {
        flex-direction: column;
        gap: 1rem;
    }

    .title {
        font-size: 2rem;
    }

    .weather-card {
        padding: 1.5rem;
    }

    .temperature-section {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .temperature {
        font-size: 3rem;
    }

    .weather-details {
        grid-template-columns: 1fr;
    }

    .city-name {
        font-size: 1.5rem;
    }

    .forecast-container {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.75rem;
    }

    .forecast-card {
        padding: 1rem 0.75rem;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 1.8rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .weather-card {
        padding: 1rem;
    }

    .temperature {
        font-size: 2.5rem;
    }

    .search-input, .search-btn {
        padding: 0.75rem 1rem;
    }

    .forecast-container {
        grid-template-columns: repeat(2, 1fr);
    }

    .history-items {
        justify-content: center;
    }
}

/* Weather Icon Animations */
.weather-icon {
    transition: transform 0.3s ease;
}

.weather-icon:hover {
    transform: scale(1.1) rotate(5deg);
}

/* Animated weather conditions */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.weather-icon-animated {
    animation: bounce 2s infinite;
}

/* Smooth transitions for theme switching */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--secondary-bg);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--accent-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent-hover);
}

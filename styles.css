/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 2rem;
    color: white;
}

.title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.title i {
    margin-right: 0.5rem;
    color: #ffeaa7;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Main Content */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

/* Search Section */
.search-section {
    width: 100%;
    max-width: 500px;
}

.search-form {
    width: 100%;
}

.search-input-container {
    display: flex;
    background: white;
    border-radius: 50px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.search-input-container:focus-within {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.search-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    outline: none;
    font-size: 1rem;
    background: transparent;
}

.search-input::placeholder {
    color: #999;
}

.search-btn {
    padding: 1rem 1.5rem;
    background: #0984e3;
    color: white;
    border: none;
    cursor: pointer;
    transition: background 0.3s ease;
    font-size: 1rem;
}

.search-btn:hover {
    background: #0770c4;
}

/* Loading Indicator */
.loading {
    display: none;
    text-align: center;
    color: white;
    margin: 2rem 0;
}

.loading.show {
    display: block;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error Message */
.error-message {
    display: none;
    background: #ff6b6b;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    max-width: 500px;
    width: 100%;
}

.error-message.show {
    display: block;
}

.error-message i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

/* Weather Section */
.weather-section {
    display: none;
    width: 100%;
    max-width: 600px;
}

.weather-section.show {
    display: block;
}

.weather-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Weather Header */
.weather-header {
    text-align: center;
    margin-bottom: 2rem;
}

.city-name {
    font-size: 2rem;
    font-weight: 600;
    color: #2d3436;
    margin-bottom: 0.5rem;
}

.date {
    color: #636e72;
    font-size: 1rem;
}

/* Main Weather Info */
.weather-main {
    margin-bottom: 2rem;
}

.temperature-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.temperature-display {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.temperature {
    font-size: 4rem;
    font-weight: 300;
    color: #2d3436;
}

.unit-toggle {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.unit-btn {
    padding: 0.25rem 0.5rem;
    border: 2px solid #ddd;
    background: white;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.unit-btn.active {
    background: #0984e3;
    color: white;
    border-color: #0984e3;
}

.weather-icon-container {
    text-align: center;
}

.weather-icon {
    width: 80px;
    height: 80px;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.weather-description {
    text-align: center;
}

.condition {
    font-size: 1.5rem;
    font-weight: 500;
    color: #2d3436;
    text-transform: capitalize;
    margin-bottom: 0.5rem;
}

.feels-like {
    color: #636e72;
    font-size: 1rem;
}

/* Weather Details */
.weather-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
    padding-top: 2rem;
    border-top: 1px solid #e9ecef;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 10px;
    transition: transform 0.2s ease;
}

.detail-item:hover {
    transform: translateY(-2px);
}

.detail-item i {
    font-size: 1.2rem;
    color: #0984e3;
    width: 20px;
    text-align: center;
}

.detail-info {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 0.85rem;
    color: #636e72;
    font-weight: 500;
}

.detail-value {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3436;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .title {
        font-size: 2rem;
    }
    
    .weather-card {
        padding: 1.5rem;
    }
    
    .temperature-section {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .temperature {
        font-size: 3rem;
    }
    
    .weather-details {
        grid-template-columns: 1fr;
    }
    
    .city-name {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .title {
        font-size: 1.8rem;
    }
    
    .subtitle {
        font-size: 1rem;
    }
    
    .weather-card {
        padding: 1rem;
    }
    
    .temperature {
        font-size: 2.5rem;
    }
    
    .search-input, .search-btn {
        padding: 0.75rem 1rem;
    }
}

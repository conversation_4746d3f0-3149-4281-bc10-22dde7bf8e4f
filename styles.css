/* Modern Weather App - Complete Design System */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

/* CSS Variables for Advanced Theme Support */
:root {
    /* Light Theme - Reduced Overlay with Background Image */
    --primary-bg:
        linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 50%, rgba(240, 147, 251, 0.3) 100%),
        url('https://images.unsplash.com/photo-1580193769210-b8d1c049a7d9?q=80&w=2074&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D');
    --secondary-bg: rgba(255, 255, 255, 0.75);
    --card-bg: rgba(255, 255, 255, 0.7);
    --glass-bg: rgba(255, 255, 255, 0.15);
    --text-primary: #2d3436;
    --text-secondary: #636e72;
    --text-light: rgba(255, 255, 255, 0.98);
    --text-muted: rgba(45, 52, 54, 0.75);

    /* Enhanced Color Palette */
    --accent-primary: #6c5ce7;
    --accent-secondary: #a29bfe;
    --accent-hover: #5f3dc4;
    --accent-gradient: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
    --accent-gradient-vibrant: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    --title-gradient: linear-gradient(135deg, #ffffff 0%, #f8f9fa 30%, #ffeaa7 70%, #ffffff 100%);
    --subtitle-gradient: linear-gradient(135deg, #fdcb6e 0%, #e17055 50%, #fd79a8 100%);

    /* Status Colors */
    --success-color: #00b894;
    --success-light: rgba(0, 184, 148, 0.1);
    --error-color: #e17055;
    --error-light: rgba(225, 112, 85, 0.1);
    --warning-color: #fdcb6e;
    --warning-light: rgba(253, 203, 110, 0.1);
    --info-color: #74b9ff;
    --info-light: rgba(116, 185, 255, 0.1);

    /* Border and Shadow System */
    --border-color: rgba(255, 255, 255, 0.2);
    --border-solid: rgba(108, 92, 231, 0.2);
    --shadow-xs: 0 2px 8px rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 4px 16px rgba(0, 0, 0, 0.08);
    --shadow-md: 0 8px 32px rgba(0, 0, 0, 0.12);
    --shadow-lg: 0 16px 48px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 24px 64px rgba(0, 0, 0, 0.2);
    --shadow-glow: 0 0 32px rgba(108, 92, 231, 0.3);

    /* Reduced Effects for Better Background Visibility */
    --backdrop-blur: blur(15px);
    --backdrop-saturate: saturate(150%);
    --border-radius-sm: 8px;
    --border-radius-md: 16px;
    --border-radius-lg: 24px;
    --border-radius-xl: 32px;

    /* Animation Variables */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    --spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Dark Theme - Enhanced Dark Mode */
[data-theme="dark"] {
    --primary-bg: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    --secondary-bg: rgba(26, 26, 46, 0.95);
    --card-bg: rgba(22, 33, 62, 0.98);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --text-primary: #f8f9fa;
    --text-secondary: #adb5bd;
    --text-light: rgba(248, 249, 250, 0.95);
    --text-muted: rgba(173, 181, 189, 0.7);

    --accent-primary: #a29bfe;
    --accent-secondary: #6c5ce7;
    --accent-hover: #b8b0ff;
    --accent-gradient: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);

    --success-color: #00d2d3;
    --error-color: #ff6b6b;
    --warning-color: #feca57;
    --info-color: #54a0ff;

    --border-color: rgba(255, 255, 255, 0.1);
    --border-solid: rgba(162, 155, 254, 0.2);
    --shadow-xs: 0 2px 8px rgba(0, 0, 0, 0.3);
    --shadow-sm: 0 4px 16px rgba(0, 0, 0, 0.4);
    --shadow-md: 0 8px 32px rgba(0, 0, 0, 0.5);
    --shadow-lg: 0 16px 48px rgba(0, 0, 0, 0.6);
    --shadow-xl: 0 24px 64px rgba(0, 0, 0, 0.7);
    --shadow-glow: 0 0 32px rgba(162, 155, 254, 0.4);
}

/* Base Typography and Layout */
html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--primary-bg);
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
    transition: all var(--transition-normal);
    overflow-x: hidden;
    position: relative;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Reduced Background Enhancement Overlay */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(240, 147, 251, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: -100;
}

/* Minimal light mode overlay for subtle contrast */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.03);
    pointer-events: none;
    z-index: -99;
}

/* Remove overlay in dark mode */
[data-theme="dark"] body::after {
    display: none;
}

/* Compact Container System */
.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 1rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1;
}

/* Floating Elements */
.floating-orb {
    position: fixed;
    border-radius: 50%;
    background: var(--accent-gradient);
    opacity: 0.1;
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
    z-index: -10;
}

.floating-orb:nth-child(1) {
    width: 300px;
    height: 300px;
    top: 10%;
    left: -150px;
    animation-delay: 0s;
}

.floating-orb:nth-child(2) {
    width: 200px;
    height: 200px;
    top: 60%;
    right: -100px;
    animation-delay: 7s;
}

.floating-orb:nth-child(3) {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 20%;
    animation-delay: 14s;
}

/* Extended Header Design */
.header {
    text-align: center;
    margin-bottom: 1rem;
    color: var(--text-light);
    position: relative;
    z-index: 2;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

.header-top-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    position: relative;
    z-index: 1;
}

.header-top::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--accent-gradient);
    opacity: 0.05;
    z-index: -1;
}

.title {
    font-size: 1.6rem;
    font-weight: 700;
    background: var(--title-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    letter-spacing: -0.01em;
    /* Enhanced gradient visibility */
    background-size: 200% 200%;
    animation: gradient-flow 4s ease-in-out infinite;
}





@keyframes pulse-glow {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
    }
    50% {
        transform: scale(1.1) rotate(5deg);
        filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8));
    }
}

@keyframes gradient-flow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Simple Icon Color Override */
.title .fas,
.welcome-title .fas {
    color: #ffd700 !important;
    background: transparent !important;
    -webkit-background-clip: initial !important;
    -webkit-text-fill-color: #ffd700 !important;
    background-clip: initial !important;
    animation: icon-glow 3s ease-in-out infinite;
}

@keyframes icon-glow {
    0%, 100% {
        filter: drop-shadow(0 0 8px rgba(255, 215, 0, 0.6));
        transform: scale(1);
    }
    50% {
        filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.9));
        transform: scale(1.05);
    }
}

.header-controls {
    display: flex;
    gap: 1rem;
}

.theme-toggle,
.location-btn {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
    color: var(--text-light);
    padding: 1rem;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-width: 48px;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle::before,
.location-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--accent-gradient);
    transition: left var(--transition-normal);
    z-index: -1;
}

.theme-toggle:hover::before,
.location-btn:hover::before {
    left: 0;
}

.theme-toggle:hover,
.location-btn:hover {
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--shadow-glow);
}

.subtitle {
    font-size: 1rem;
    font-weight: 500;
    color: #ffffff;
    opacity: 0.9;
    margin: 0 0 1.5rem 0;
    letter-spacing: 0.01em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Integrated Header Search Section */
.header-search-integrated {
    width: 100%;
    position: relative;
    z-index: 10;
    pointer-events: auto;
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    justify-content: center;
}

.search-central {
    flex: 0 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    min-width: 400px;
}

.search-central .search-form {
    width: 100%;
    margin: 0;
}

.search-central .search-input-container {
    display: flex;
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
    border: 2px solid rgba(255, 255, 255, 0.3);
    position: relative;
    pointer-events: auto;
}

.search-central .search-input-container::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: var(--accent-gradient);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
    pointer-events: none;
}

.search-central .search-input-container:focus-within::before {
    opacity: 1;
}

.search-central .search-input-container:focus-within {
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
    border-color: rgba(255, 255, 255, 0.5);
}

.search-central .search-input {
    flex: 1;
    padding: 1.25rem 2rem;
    border: none;
    outline: none;
    font-size: 1.1rem;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-weight: 500;
    position: relative;
    z-index: 1;
    pointer-events: auto;
    cursor: text;
    border-radius: var(--border-radius-xl) 0 0 var(--border-radius-xl);
}

.search-central .search-input::placeholder {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
}

/* Ensure input is interactive */
.header-search .search-input:focus {
    color: var(--text-light);
    background: transparent;
}

.header-search .search-input::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.header-search .search-input::-moz-placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.header-search .search-input:-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-central .search-btn {
    padding: 1.25rem 2rem;
    background: var(--accent-gradient);
    color: white;
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: 1.1rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    min-width: 60px;
    z-index: 1;
    border-radius: 0 var(--border-radius-xl) var(--border-radius-xl) 0;
}

.search-central .search-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-normal);
}

.search-central .search-btn:hover::before {
    left: 100%;
}

.search-central .search-btn:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.search-central .search-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Integrated Header Search History */
.header-search-integrated .search-history-left,
.header-search-integrated .search-history-right {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    min-width: 180px;
    max-width: 220px;
    flex: 1;
}

.header-search-integrated .search-history-left {
    align-items: flex-end;
}

.header-search-integrated .search-history-right {
    align-items: flex-start;
}

.header-search-integrated .history-row {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.header-search-integrated .search-history-left .history-row {
    justify-content: flex-end;
}

.header-search-integrated .search-history-right .history-row {
    justify-content: flex-start;
}

.header-search-integrated .history-side-item {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-md);
    padding: 0.6rem 0.9rem;
    color: var(--text-light);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(10px) scale(0.95);
    animation: fadeInUp 0.3s ease forwards;
    box-shadow: var(--shadow-sm);
    white-space: nowrap;
    min-width: 85px;
    max-width: 130px;
    text-overflow: ellipsis;
}

.history-side-item:nth-child(1) { animation-delay: 0.1s; }
.history-side-item:nth-child(2) { animation-delay: 0.15s; }
.history-side-item:nth-child(3) { animation-delay: 0.2s; }
.history-side-item:nth-child(4) { animation-delay: 0.25s; }
.history-side-item:nth-child(5) { animation-delay: 0.3s; }
.history-side-item:nth-child(6) { animation-delay: 0.35s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.history-side-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
    z-index: -1;
}

.history-side-item:hover::before {
    left: 100%;
}

.header-search-integrated .history-side-item:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px) scale(1.03);
    box-shadow: var(--shadow-lg);
    color: #ffffff;
}

.history-side-item:active {
    transform: translateY(-1px) scale(1.01);
    transition: transform 0.1s ease;
}

/* Integrated Clear History Button */
.search-central .clear-history-container {
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.header-search-integrated:hover .clear-history-container {
    opacity: 1;
}

.search-central .clear-history-btn {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid rgba(255, 255, 255, 0.25);
    color: var(--text-light);
    cursor: pointer;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-md);
    transition: all var(--transition-normal);
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 0.4rem;
    font-weight: 500;
}

.search-central .clear-history-btn:hover {
    background: rgba(255, 100, 100, 0.25);
    border-color: rgba(255, 100, 100, 0.5);
    color: #ff6b6b;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Hidden original search history */
.header-search .search-history {
    display: none !important;
}

/* Compact Main Content Layout */
.main {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

/* True Full-Width Welcome Section */
.welcome-section {
    width: 100vw;
    max-width: none;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
    margin-bottom: 0.75rem;
    animation: fadeInUp 0.8s var(--spring);
    position: relative;
}

.welcome-section.hidden {
    display: none;
}

.welcome-content {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
    border-radius: 0;
    padding: 1rem;
    border: none;
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    width: 100%;
}

/* Compact Full-Width Welcome Header */
.welcome-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem 2rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0;
    border: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin: -1rem -1rem 1rem -1rem;
    width: calc(100% + 2rem);
}

.welcome-header .welcome-message {
    flex: 1;
    text-align: left;
    margin-bottom: 0;
}

.welcome-header .live-datetime {
    flex: 0 0 auto;
    margin-bottom: 0;
    text-align: right;
    min-width: 250px;
}

/* Compact Full-Width Horizontal Content Layout */
.welcome-horizontal-content {
    display: grid;
    grid-template-columns: 1fr 1.2fr 1fr;
    gap: 1.5rem;
    align-items: start;
    min-height: 200px;
    padding: 0 2rem;
    max-width: none;
}

.welcome-column {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    height: 100%;
}

.welcome-left {
    padding-right: 0.5rem;
}

.welcome-center {
    padding: 0 0.5rem;
}

.welcome-right {
    padding-left: 0.5rem;
}

.welcome-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--accent-gradient);
    opacity: 0.03;
    z-index: -1;
}

.welcome-content::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg,
        transparent 0deg,
        rgba(108, 92, 231, 0.1) 90deg,
        transparent 180deg,
        rgba(162, 155, 254, 0.1) 270deg,
        transparent 360deg
    );
    animation: rotate 20s linear infinite;
    z-index: -1;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.welcome-message {
    position: relative;
    z-index: 1;
}

.welcome-title {
    font-size: 1.2rem;
    font-weight: 700;
    background: var(--title-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    letter-spacing: -0.01em;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    /* Enhanced gradient visibility */
    background-size: 200% 200%;
    animation: gradient-flow 4s ease-in-out infinite;
}





@keyframes pulse-rainbow {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.4));
    }
    33% {
        filter: drop-shadow(0 0 10px rgba(255, 165, 0, 0.4));
    }
    66% {
        filter: drop-shadow(0 0 10px rgba(255, 140, 0, 0.4));
    }
    50% {
        transform: scale(1.08) rotate(5deg);
    }
}

.welcome-subtitle {
    font-size: 0.75rem;
    color: #ffffff;
    font-weight: 500;
    line-height: 1.3;
    max-width: 400px;
    margin: 0;
    opacity: 0.9;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Compact Header Live Date and Time */
.welcome-header .live-datetime {
    background: var(--card-bg);
    backdrop-filter: var(--backdrop-blur);
    border-radius: var(--border-radius-sm);
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-solid);
    box-shadow: var(--shadow-xs);
    position: relative;
    overflow: hidden;
    min-width: 150px;
}

.live-datetime::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--accent-gradient);
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.welcome-header .current-time {
    font-size: 1rem;
    font-weight: 500;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.15rem;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    letter-spacing: 0.02em;
    text-align: center;
    animation: digital-glow 2s ease-in-out infinite;
}

@keyframes digital-glow {
    0%, 100% { filter: drop-shadow(0 0 8px rgba(108, 92, 231, 0.2)); }
    50% { filter: drop-shadow(0 0 12px rgba(108, 92, 231, 0.4)); }
}

.welcome-header .current-date {
    font-size: 0.7rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-align: center;
    letter-spacing: 0.01em;
    opacity: 0.8;
}

/* Compact Column Weather Illustration */
.welcome-column .weather-illustration {
    margin: 0;
    height: 100px;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.15), rgba(162, 155, 254, 0.15));
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.weather-scene {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.welcome-column .sun {
    position: absolute;
    width: 30px;
    height: 30px;
    background: radial-gradient(circle, #ffd700 0%, #ffed4e 70%);
    border-radius: 50%;
    top: 15px;
    right: 20%;
    animation: rotate 20s linear infinite;
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.4);
}

.sun-rays {
    position: absolute;
    width: 120px;
    height: 120px;
    top: -20px;
    left: -20px;
    background: conic-gradient(
        transparent 0deg,
        rgba(255, 215, 0, 0.3) 10deg,
        transparent 20deg,
        rgba(255, 215, 0, 0.3) 30deg,
        transparent 40deg,
        rgba(255, 215, 0, 0.3) 50deg,
        transparent 60deg,
        rgba(255, 215, 0, 0.3) 70deg,
        transparent 80deg
    );
    border-radius: 50%;
    animation: rotate 30s linear infinite reverse;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.clouds {
    position: absolute;
    width: 100%;
    height: 100%;
}

.cloud {
    position: absolute;
    background: var(--text-light);
    border-radius: 50px;
    opacity: 0.8;
}

.cloud::before,
.cloud::after {
    content: '';
    position: absolute;
    background: var(--text-light);
    border-radius: 50px;
}

.cloud-1 {
    width: 60px;
    height: 30px;
    top: 60px;
    left: 10%;
    animation: float 6s ease-in-out infinite;
}

.cloud-1::before {
    width: 40px;
    height: 40px;
    top: -20px;
    left: 5px;
}

.cloud-1::after {
    width: 30px;
    height: 30px;
    top: -15px;
    right: 5px;
}

.cloud-2 {
    width: 80px;
    height: 40px;
    top: 40px;
    left: 60%;
    animation: float 8s ease-in-out infinite reverse;
}

.cloud-2::before {
    width: 50px;
    height: 50px;
    top: -25px;
    left: 10px;
}

.cloud-2::after {
    width: 40px;
    height: 40px;
    top: -20px;
    right: 10px;
}

.cloud-3 {
    width: 50px;
    height: 25px;
    top: 100px;
    left: 30%;
    animation: float 7s ease-in-out infinite;
}

.cloud-3::before {
    width: 35px;
    height: 35px;
    top: -18px;
    left: 5px;
}

.cloud-3::after {
    width: 25px;
    height: 25px;
    top: -12px;
    right: 5px;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) translateX(0px); }
    25% { transform: translateY(-10px) translateX(5px); }
    50% { transform: translateY(0px) translateX(10px); }
    75% { transform: translateY(-5px) translateX(5px); }
}

.rain-drops {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.5s ease;
}

.rain-drops.active {
    opacity: 1;
}

.rain-drop {
    position: absolute;
    width: 2px;
    height: 15px;
    background: linear-gradient(to bottom, transparent, var(--accent-color));
    border-radius: 0 0 2px 2px;
    animation: fall 1s linear infinite;
}

@keyframes fall {
    from {
        transform: translateY(-20px);
        opacity: 1;
    }
    to {
        transform: translateY(200px);
        opacity: 0;
    }
}

/* Enhanced Center Column City Suggestions */
.welcome-center .example-cities {
    margin: 0;
}

.welcome-center .example-title {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: 0.75rem;
    font-weight: 600;
    text-align: center;
    letter-spacing: 0.01em;
}

.welcome-center .city-suggestions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    justify-content: center;
}

.welcome-center .city-suggestion {
    background: var(--card-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: 0.5rem 0.75rem;
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: 0.75rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    position: relative;
    overflow: hidden;
    min-height: 32px;
    text-align: center;
    box-shadow: var(--shadow-xs);
}

.city-suggestion::before {
    content: '';
    position: absolute;
    inset: 0;
    background: var(--accent-gradient);
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -1;
}

.city-suggestion::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transition: all var(--transition-normal);
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

.city-suggestion:hover::before {
    opacity: 1;
}

.city-suggestion:hover::after {
    width: 200px;
    height: 200px;
}

.city-suggestion:hover {
    color: white;
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-glow);
}

.city-suggestion i {
    font-size: 1rem;
    transition: transform var(--transition-normal);
}

.city-suggestion:hover i {
    transform: scale(1.2);
}

/* Compact Center Column Weather Fact */
.welcome-center .weather-fact {
    background: var(--card-bg);
    border-radius: var(--border-radius-md);
    padding: 0.75rem;
    margin: 0;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    text-align: left;
    position: relative;
    overflow: hidden;
}

.weather-fact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: var(--accent-gradient);
}

.fact-icon {
    background: var(--accent-gradient);
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    animation: glow 3s ease-in-out infinite;
    font-size: 0.6rem;
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 8px rgba(108, 92, 231, 0.2); }
    50% { box-shadow: 0 0 16px rgba(108, 92, 231, 0.4); }
}

.fact-content {
    flex: 1;
}

.fact-title {
    font-size: 0.7rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.15rem;
}

.fact-text {
    font-size: 0.65rem;
    color: var(--text-secondary);
    line-height: 1.2;
    font-style: italic;
}

/* Compact Left Column Quick Stats */
.welcome-left .quick-stats {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
    margin: 0;
}

.welcome-left .stat-item {
    background: var(--card-bg);
    border-radius: var(--border-radius-md);
    padding: 0.75rem;
    text-align: center;
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-xs);
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--accent-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.stat-item:hover::before {
    transform: scaleX(1);
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-item i {
    font-size: 0.8rem;
    color: var(--accent-primary);
    margin-bottom: 0.15rem;
}

.stat-number {
    display: block;
    font-size: 0.8rem;
    font-weight: 700;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.1rem;
}

.stat-label {
    font-size: 0.55rem;
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.2px;
    opacity: 0.75;
}

/* Right Column Smart Features */
.welcome-right .smart-features {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
    margin: 0;
}

.welcome-right .feature-item {
    background: var(--card-bg);
    border-radius: var(--border-radius-md);
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-xs);
}

.feature-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent, rgba(108, 92, 231, 0.03), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.feature-item:hover::before {
    transform: translateX(100%);
}

.feature-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.feature-icon {
    background: var(--accent-gradient);
    color: white;
    width: 28px;
    height: 28px;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    font-size: 0.7rem;
}

.feature-content h4 {
    font-size: 0.7rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.15rem;
}

.feature-content p {
    font-size: 0.6rem;
    color: var(--text-secondary);
    line-height: 1.2;
    opacity: 0.9;
}

/* Compact Right Column Weather Insights */
.welcome-right .weather-insights {
    background: var(--card-bg);
    border-radius: var(--border-radius-md);
    padding: 0.75rem;
    margin: 0;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.insight-header {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    margin-bottom: 0.5rem;
    font-size: 0.7rem;
    font-weight: 600;
    color: var(--text-primary);
}

.insight-header i {
    color: var(--accent-primary);
    font-size: 0.65rem;
}

.insights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    gap: 0.4rem;
}

.insight-item {
    text-align: center;
    padding: 0.4rem;
    background: rgba(108, 92, 231, 0.04);
    border-radius: var(--border-radius-sm);
    border: 1px solid rgba(108, 92, 231, 0.08);
    transition: all var(--transition-normal);
}

.insight-item:hover {
    background: rgba(108, 92, 231, 0.08);
    transform: translateY(-1px);
}

.insight-value {
    display: block;
    font-size: 0.8rem;
    font-weight: 700;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.1rem;
}

.insight-label {
    font-size: 0.55rem;
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.2px;
    opacity: 0.8;
}



/* Note: Search section moved to header */

.search-input-container {
    display: flex;
    background: var(--card-bg);
    backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
    border: 2px solid transparent;
    position: relative;
}

.search-input-container::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: var(--accent-gradient);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.search-input-container:focus-within::before {
    opacity: 1;
}

.search-input-container:focus-within {
    transform: translateY(-4px);
    box-shadow: var(--shadow-glow);
}

.search-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: none;
    outline: none;
    font-size: 1rem;
    background: transparent;
    color: var(--text-primary);
    font-weight: 500;
}

.search-input::placeholder {
    color: var(--text-muted);
    font-weight: 400;
}

.search-btn {
    padding: 1rem 1.5rem;
    background: var(--accent-gradient);
    color: white;
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: 1rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    min-width: 50px;
}

.search-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-normal);
}

.search-btn:hover::before {
    left: 100%;
}

.search-btn:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.search-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Search History Styles */
.search-history {
    margin-top: 1rem;
    background: var(--secondary-bg);
    border-radius: 15px;
    padding: 1rem;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
    display: none;
}

.search-history.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.history-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-secondary);
}

.clear-history-btn {
    background: none;
    border: none;
    color: var(--error-color);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.clear-history-btn:hover {
    background: rgba(225, 112, 85, 0.1);
}

.history-items {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.history-item {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    color: var(--text-primary);
}

.history-item:hover {
    background: var(--accent-color);
    color: white;
    transform: translateY(-2px);
}

/* Enhanced Loading Indicator */
.loading {
    display: none;
    text-align: center;
    color: var(--text-light);
    margin: 2rem 0;
}

.loading.show {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.loading-content {
    background: var(--secondary-bg);
    border-radius: 20px;
    padding: 2rem;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--border-color);
    max-width: 300px;
    margin: 0 auto;
}

.spinner-container {
    position: relative;
    margin-bottom: 1rem;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(108, 92, 231, 0.3);
    border-top: 4px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.shimmer-effect {
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(108, 92, 231, 0.2),
        transparent
    );
    border-radius: 50%;
    animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

.loading-text {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
}

/* Enhanced Error Message */
.error-message {
    display: none;
    background: var(--error-color);
    color: white;
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(225, 112, 85, 0.3);
    max-width: 500px;
    width: 100%;
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.error-message.show {
    display: block;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-message i {
    margin-right: 0.5rem;
    font-size: 1.2rem;
}

.retry-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    margin-top: 1rem;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.retry-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.retry-btn i {
    margin-right: 0.5rem;
}

/* Weather Section */
.weather-section {
    display: none;
    width: 100%;
    max-width: 600px;
}

.weather-section.show {
    display: block;
}

/* Ultra Compact Weather Card */
.weather-card {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
    border-radius: var(--border-radius-md);
    padding: 1rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all var(--transition-slow);
    position: relative;
    overflow: hidden;
    max-width: 500px;
    width: 100%;
}

.weather-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--accent-gradient);
    opacity: 0.02;
    z-index: -1;
}

.weather-card::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--accent-gradient);
    border-radius: inherit;
    opacity: 0;
    transition: opacity var(--transition-normal);
    z-index: -2;
}

.weather-card:hover::after {
    opacity: 0.1;
}

.weather-card:hover {
    transform: translateY(-8px) scale(1.01);
    box-shadow: var(--shadow-glow);
}

/* Ultra Compact Weather Header */
.weather-header {
    text-align: center;
    margin-bottom: 1rem;
}

.city-name {
    font-size: 1.2rem;
    font-weight: 700;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.25rem;
    letter-spacing: -0.01em;
}

.date {
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 500;
    opacity: 0.75;
}

/* Ultra Compact Main Weather Info */
.weather-main {
    margin-bottom: 1rem;
}

.temperature-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.temperature-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.temperature {
    font-size: 2.2rem;
    font-weight: 400;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 1px 2px rgba(108, 92, 231, 0.2));
}

.unit-toggle {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

.unit-btn {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    background: var(--card-bg);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    font-size: 0.7rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    color: var(--text-secondary);
}

.unit-btn.active {
    background: var(--accent-gradient);
    color: white;
    border-color: transparent;
    box-shadow: var(--shadow-sm);
}

.weather-icon-container {
    text-align: center;
}

.weather-icon {
    width: 50px;
    height: 50px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
    transition: transform var(--transition-normal);
}

.weather-icon:hover {
    transform: scale(1.05);
}

.weather-description {
    text-align: center;
}

.condition {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    text-transform: capitalize;
    margin-bottom: 0.25rem;
}

.feels-like {
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: 500;
}

/* Ultra Compact Weather Mood */
.weather-mood {
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.06), rgba(108, 92, 231, 0.03));
    border-radius: var(--border-radius-sm);
    padding: 0.75rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(108, 92, 231, 0.12);
    animation: moodPulse 3s ease-in-out infinite;
}

@keyframes moodPulse {
    0%, 100% { box-shadow: 0 0 15px rgba(108, 92, 231, 0.08); }
    50% { box-shadow: 0 0 25px rgba(108, 92, 231, 0.15); }
}

.mood-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.mood-emoji {
    font-size: 1.8rem;
    animation: bounce 2s ease-in-out infinite;
}

.mood-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

.mood-title {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-primary);
}

.mood-activity {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-style: italic;
    opacity: 0.85;
}

/* Ultra Compact Weather Details */
.weather-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    padding: 0.5rem;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.detail-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.detail-item i {
    font-size: 0.9rem;
    color: var(--accent-primary);
    width: 16px;
    text-align: center;
}

.detail-info {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-size: 0.65rem;
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.01em;
    opacity: 0.75;
}

.detail-value {
    font-size: 0.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-top: 0.05rem;
}

/* Ultra Compact 5-Day Forecast */
.forecast-section {
    display: none;
    width: 100%;
    max-width: 400px;
    margin-top: 0.5rem;
}

.forecast-section.show {
    display: block;
    animation: fadeInUp 0.6s var(--spring);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.forecast-header {
    text-align: center;
    margin-bottom: 0.3rem;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border-radius: var(--border-radius-sm);
    padding: 0.25rem;
    border: 1px solid var(--border-color);
}

.forecast-title {
    font-size: 0.7rem;
    font-weight: 700;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.01em;
}

.forecast-title i {
    margin-right: 0.3rem;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: bounce 2s ease-in-out infinite;
    font-size: 0.8rem;
}

.forecast-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(50px, 1fr));
    gap: 0.2rem;
}

.forecast-card {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
    border-radius: var(--border-radius-sm);
    padding: 0.25rem 0.2rem;
    text-align: center;
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-xs);
    position: relative;
    overflow: hidden;
}

.forecast-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--accent-gradient);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.forecast-card:hover::before {
    transform: scaleX(1);
}

.forecast-card:hover {
    transform: translateY(-4px) scale(1.01);
    box-shadow: var(--shadow-md);
}

.forecast-day {
    font-size: 0.6rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.15rem;
    text-transform: uppercase;
    letter-spacing: 0.01em;
}

.forecast-icon {
    width: 16px;
    height: 16px;
    margin: 0.1rem auto;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    transition: transform var(--transition-normal);
}

.forecast-card:hover .forecast-icon {
    transform: scale(1.1);
}

.forecast-temps {
    margin-top: 0.15rem;
}

.forecast-high {
    font-size: 0.7rem;
    font-weight: 700;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.forecast-low {
    font-size: 0.6rem;
    color: var(--text-secondary);
    margin-left: 0.2rem;
    opacity: 0.75;
}

.forecast-desc {
    font-size: 0.55rem;
    color: var(--text-secondary);
    margin-top: 0.15rem;
    text-transform: capitalize;
    opacity: 0.8;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 1rem;
    color: var(--text-light);
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Modern Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    /* Floating orbs adjustment for mobile */
    .floating-orb {
        display: none;
    }

    /* Header Mobile */
    .header-top {
        flex-direction: column;
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .title {
        font-size: 2.2rem;
        flex-direction: column;
        gap: 0.75rem;
    }

    .header-controls {
        gap: 0.75rem;
    }

    /* Compact Full-Width Welcome Section Mobile */
    .welcome-section {
        width: 100vw;
        margin-left: calc(-50vw + 50%);
        margin-right: calc(-50vw + 50%);
    }

    .welcome-content {
        padding: 0.75rem;
        border-radius: 0;
    }

    .welcome-header {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
        margin: -0.75rem -0.75rem 0.75rem -0.75rem;
        padding: 0.75rem 1rem;
        width: calc(100% + 1.5rem);
    }

    .welcome-header .welcome-message {
        text-align: center;
    }

    .welcome-header .live-datetime {
        text-align: center;
        min-width: auto;
        padding: 0.4rem 0.6rem;
    }

    .welcome-horizontal-content {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 1rem;
        min-height: 150px;
    }

    .welcome-left,
    .welcome-center,
    .welcome-right {
        padding: 0;
    }

    .welcome-title {
        font-size: 1rem;
        justify-content: center;
    }

    .welcome-subtitle {
        font-size: 1rem;
    }

    .current-time {
        font-size: 2rem;
    }

    .live-datetime {
        padding: 1rem;
    }

    /* Weather Illustration Mobile */
    .weather-illustration {
        height: 150px;
    }

    .sun {
        width: 60px;
        height: 60px;
        top: 15px;
    }

    /* City Suggestions Mobile */
    .city-suggestions {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .city-suggestion {
        padding: 0.75rem 1rem;
        font-size: 0.9rem;
        min-height: 48px;
    }

    /* Smart Features Mobile */
    .weather-fact {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
        padding: 1.5rem;
    }

    .quick-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .smart-features {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .feature-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
        padding: 1.5rem;
    }

    .insights-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    /* Weather Card Mobile */
    .weather-card {
        padding: 1.5rem;
    }

    .temperature-section {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .temperature {
        font-size: 2.8rem;
    }

    .weather-details {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .city-name {
        font-size: 1.4rem;
    }

    .weather-mood {
        padding: 1rem;
    }

    .mood-emoji {
        font-size: 2rem;
    }

    /* Compact Forecast Mobile */
    .forecast-container {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.4rem;
    }

    .forecast-card {
        padding: 0.5rem 0.4rem;
    }

    .forecast-header {
        padding: 0.5rem;
    }

    .forecast-title {
        font-size: 0.9rem;
    }

    /* Extended Header Mobile */
    .header {
        padding: 1rem;
    }

    .header-top-row {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
    }

    .subtitle {
        margin-bottom: 1rem;
        padding-bottom: 0.75rem;
    }

    .header-search-integrated {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .search-central {
        order: 1;
        min-width: auto;
        width: 100%;
        max-width: 400px;
    }

    .search-history-left,
    .search-history-right {
        order: 2;
        min-width: auto;
        max-width: 100%;
        align-items: center;
    }

    .header-search-integrated .search-history-left .history-row,
    .header-search-integrated .search-history-right .history-row {
        justify-content: center;
    }

    .header-search-integrated .history-side-item {
        font-size: 0.75rem;
        padding: 0.5rem 0.7rem;
        min-width: 70px;
        max-width: 100px;
    }

    .search-central .clear-history-container {
        opacity: 1;
        margin-top: 0.5rem;
    }

    .search-central .search-input-container {
        border-radius: var(--border-radius-lg);
    }

    .search-central .search-input {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        border-radius: var(--border-radius-lg) 0 0 var(--border-radius-lg);
    }

    .search-central .search-btn {
        padding: 1rem 1.5rem;
        border-radius: 0 var(--border-radius-lg) var(--border-radius-lg) 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0.75rem;
    }

    /* Header Small Mobile */
    .title {
        font-size: 1.8rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .header-top {
        padding: 1rem;
    }

    /* Welcome Section Small Mobile */
    .welcome-content {
        padding: 1.25rem 0.75rem;
    }

    .welcome-title {
        font-size: 1.5rem;
    }

    .welcome-subtitle {
        font-size: 0.9rem;
    }

    .current-time {
        font-size: 1.8rem;
    }

    .live-datetime {
        padding: 0.75rem;
    }

    .weather-illustration {
        height: 120px;
    }

    /* City Suggestions Small Mobile */
    .city-suggestions {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .city-suggestion {
        padding: 1rem;
        font-size: 0.9rem;
        min-height: 52px;
    }

    /* Stats Small Mobile */
    .quick-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
    }

    .stat-item {
        padding: 1rem 0.75rem;
    }

    .stat-number {
        font-size: 1.3rem;
    }

    /* Smart Features Small Mobile */
    .smart-features {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .feature-item {
        padding: 1.25rem;
    }

    .insights-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
    }

    .insight-item {
        padding: 1rem 0.75rem;
    }

    .insight-value {
        font-size: 1.3rem;
    }

    /* Weather Card Small Mobile */
    .weather-card {
        padding: 1.25rem 0.75rem;
    }

    .temperature {
        font-size: 2.5rem;
    }

    .city-name {
        font-size: 1.3rem;
    }

    .weather-details {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .detail-item {
        padding: 0.5rem;
    }

    /* Header Search Small Mobile */
    .header-search {
        gap: 0.5rem;
    }

    .history-row {
        gap: 0.4rem;
    }

    .history-side-item {
        font-size: 0.7rem;
        padding: 0.35rem 0.5rem;
        min-width: 60px;
        max-width: 90px;
    }

    .header-search .search-input {
        padding: 0.75rem;
        font-size: 0.85rem;
    }

    .header-search .search-btn {
        padding: 0.75rem;
    }

    /* Compact Forecast Small Mobile */
    .forecast-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .forecast-card {
        padding: 0.6rem 0.5rem;
    }

    .forecast-title {
        font-size: 1.3rem;
    }

    /* History Small Mobile */
    .history-items {
        justify-content: center;
        gap: 0.5rem;
    }

    .history-item {
        font-size: 0.85rem;
        padding: 0.5rem 1rem;
    }
}

/* Weather Icon Animations */
.weather-icon {
    transition: transform 0.3s ease;
}

.weather-icon:hover {
    transform: scale(1.1) rotate(5deg);
}

/* Animated weather conditions */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.weather-icon-animated {
    animation: bounce 2s infinite;
}

/* Smooth transitions for theme switching */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Modern Scrollbar */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: var(--glass-bg);
    border-radius: var(--border-radius-sm);
}

::-webkit-scrollbar-thumb {
    background: var(--accent-gradient);
    border-radius: var(--border-radius-sm);
    border: 2px solid transparent;
    background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--accent-hover), var(--accent-primary));
    background-clip: padding-box;
}

/* Modern Selection */
::selection {
    background: var(--accent-primary);
    color: white;
}

::-moz-selection {
    background: var(--accent-primary);
    color: white;
}

/* Focus Styles */
*:focus {
    outline: 2px solid var(--accent-primary);
    outline-offset: 2px;
}

button:focus,
input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.3);
}

/* Modern Loading States */
.loading-shimmer {
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(108, 92, 231, 0.1) 50%,
        transparent 100%
    );
    background-size: 200% 100%;
    animation: shimmer-loading 1.5s ease-in-out infinite;
}

@keyframes shimmer-loading {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Enhanced Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Utility Classes */
.animate-fade-in {
    animation: fadeIn 0.5s ease;
}

.animate-slide-left {
    animation: slideInFromLeft 0.6s var(--spring);
}

.animate-slide-right {
    animation: slideInFromRight 0.6s var(--spring);
}

.animate-scale-in {
    animation: scaleIn 0.4s var(--spring);
}

/* Performance Optimizations */
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}

/* Print Styles */
@media print {
    .floating-orb,
    .header-controls,
    .search-section,
    .smart-features {
        display: none !important;
    }

    .weather-card {
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}
